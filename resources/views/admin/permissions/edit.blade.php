@extends('layouts.admin')

@section('title', __('messages.edit_permission'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-key {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.edit_permission') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-outline-info {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.view') }}
            </a>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            <strong>{{ __('messages.validation_errors') }}</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- Edit Permission Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.permission_information') }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.update', $permission) }}" method="POST" id="editPermissionForm">
                        @csrf
                        @method('PUT')
                        
                        <!-- Permission Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('messages.permission_name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $permission->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.permission_name_help') }}</div>
                        </div>

                        <!-- Guard Name -->
                        <div class="mb-3">
                            <label for="guard_name" class="form-label">{{ __('messages.guard_name') }} <span class="text-danger">*</span></label>
                            <select class="form-select @error('guard_name') is-invalid @enderror" id="guard_name" name="guard_name" required>
                                <option value="web" {{ old('guard_name', $permission->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                <option value="api" {{ old('guard_name', $permission->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                            </select>
                            @error('guard_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.guard_name_help') }}</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.update') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Permission Info Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.permission_details') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.permission_name') }}:</strong></div>
                        <div class="col-sm-6">{{ $permission->name }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.guard') }}:</strong></div>
                        <div class="col-sm-6">{{ $permission->guard_name }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.created_at') }}:</strong></div>
                        <div class="col-sm-6">{{ $permission->created_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.roles_count') }}:</strong></div>
                        <div class="col-sm-6">{{ $permission->roles->count() }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.users_count') }}:</strong></div>
                        <div class="col-sm-6">{{ $permission->users->count() }}</div>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.warning') }}</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        {{ __('messages.edit_permission_warning') }}
                    </div>
                    <h6>{{ __('messages.affected_roles') }}</h6>
                    @if($permission->roles->count() > 0)
                        <ul class="small">
                            @foreach($permission->roles as $role)
                                <li>{{ $role->name }}</li>
                            @endforeach
                        </ul>
                    @else
                        <p class="small text-muted">{{ __('messages.no_roles_using_permission') }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Permission name validation
    $('#name').on('input', function() {
        const value = $(this).val();
        const isValid = /^[a-zA-Z0-9_\s-]+$/.test(value);
        
        if (!isValid && value.length > 0) {
            $(this).addClass('is-invalid');
            if (!$(this).siblings('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">{{ __("messages.permission_name_invalid_chars") }}</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });
    
    // Form validation
    $('#editPermissionForm').on('submit', function(e) {
        const name = $('#name').val().trim();
        if (!name) {
            e.preventDefault();
            alert('{{ __("messages.permission_name_required") }}');
            return false;
        }
        
        if (!/^[a-zA-Z0-9_\s-]+$/.test(name)) {
            e.preventDefault();
            alert('{{ __("messages.permission_name_invalid_chars") }}');
            return false;
        }
        
        // Confirm if permission is used by roles
        @if($permission->roles->count() > 0)
            if (!confirm('{{ __("messages.confirm_edit_permission_with_roles") }}')) {
                e.preventDefault();
                return false;
            }
        @endif
    });
});
</script>
@endpush
