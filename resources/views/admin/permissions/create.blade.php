@extends('layouts.admin')

@section('title', __('messages.create_permission'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-key {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.create_permission') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            <strong>{{ __('messages.validation_errors') }}</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- Create Permission Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.permission_information') }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.store') }}" method="POST" id="createPermissionForm">
                        @csrf
                        
                        <!-- Permission Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('messages.permission_name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required
                                   placeholder="e.g., create users, read posts, manage settings">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.permission_name_help') }}</div>
                        </div>

                        <!-- Category -->
                        <div class="mb-3">
                            <label for="category" class="form-label">{{ __('messages.category') }}</label>
                            <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                <option value="">{{ __('messages.select_category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category }}" {{ old('category') == $category ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $category)) }}
                                    </option>
                                @endforeach
                                <option value="custom">{{ __('messages.custom_category') }}</option>
                            </select>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Custom Category (hidden by default) -->
                        <div class="mb-3 d-none" id="custom-category-group">
                            <label for="custom_category" class="form-label">{{ __('messages.custom_category_name') }}</label>
                            <input type="text" class="form-control" id="custom_category" name="custom_category" 
                                   value="{{ old('custom_category') }}" placeholder="e.g., reports, analytics">
                        </div>

                        <!-- Guard Name -->
                        <div class="mb-3">
                            <label for="guard_name" class="form-label">{{ __('messages.guard_name') }} <span class="text-danger">*</span></label>
                            <select class="form-select @error('guard_name') is-invalid @enderror" id="guard_name" name="guard_name" required>
                                <option value="web" {{ old('guard_name', 'web') == 'web' ? 'selected' : '' }}>Web</option>
                                <option value="api" {{ old('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                            </select>
                            @error('guard_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.guard_name_help') }}</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('messages.description') }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.permission_description_help') }}</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.create_permission') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.permission_naming_guide') }}</h6>
                </div>
                <div class="card-body">
                    <h6>{{ __('messages.naming_conventions') }}</h6>
                    <ul class="small text-muted">
                        <li><code>create users</code> - {{ __('messages.create_users_example') }}</li>
                        <li><code>read posts</code> - {{ __('messages.read_posts_example') }}</li>
                        <li><code>update settings</code> - {{ __('messages.update_settings_example') }}</li>
                        <li><code>delete comments</code> - {{ __('messages.delete_comments_example') }}</li>
                    </ul>
                </div>
            </div>

            <div class="card shadow mb-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.categories') }}</h6>
                </div>
                <div class="card-body">
                    <h6>{{ __('messages.available_categories') }}</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($categories as $category)
                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $category)) }}</span>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">{{ __('messages.bulk_create_tip') }}</h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted">{{ __('messages.bulk_create_description') }}</p>
                    <a href="{{ route('admin.permissions.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus-circle {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.bulk_create') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Show/hide custom category input
    $('#category').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom-category-group').removeClass('d-none');
            $('#custom_category').prop('required', true);
        } else {
            $('#custom-category-group').addClass('d-none');
            $('#custom_category').prop('required', false);
        }
    });
    
    // Permission name validation
    $('#name').on('input', function() {
        const value = $(this).val();
        const isValid = /^[a-zA-Z0-9_\s-]+$/.test(value);
        
        if (!isValid && value.length > 0) {
            $(this).addClass('is-invalid');
            if (!$(this).siblings('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">{{ __("messages.permission_name_invalid_chars") }}</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });
    
    // Form validation
    $('#createPermissionForm').on('submit', function(e) {
        const name = $('#name').val().trim();
        if (!name) {
            e.preventDefault();
            alert('{{ __("messages.permission_name_required") }}');
            return false;
        }
        
        if (!/^[a-zA-Z0-9_\s-]+$/.test(name)) {
            e.preventDefault();
            alert('{{ __("messages.permission_name_invalid_chars") }}');
            return false;
        }
    });
});
</script>
@endpush
