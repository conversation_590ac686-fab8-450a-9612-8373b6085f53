<div class="btn-group" role="group">
    @can('read permissions')
        <a href="{{ route('admin.permissions.show', $id) }}" 
           class="btn btn-sm btn-outline-info" 
           title="{{ __('messages.view_permission') }}">
            <i class="fas fa-eye"></i>
        </a>
    @endcan
    
    @can('update permissions')
        <a href="{{ route('admin.permissions.edit', $id) }}" 
           class="btn btn-sm btn-outline-primary" 
           title="{{ __('messages.edit_permission') }}">
            <i class="fas fa-edit"></i>
        </a>
    @endcan
    
    @can('delete permissions')
        <button type="button" 
                class="btn btn-sm btn-outline-danger" 
                onclick="deletePermission({{ $id }}, '{{ $name }}')"
                title="{{ __('messages.delete_permission') }}">
            <i class="fas fa-trash"></i>
        </button>
    @endcan
</div>

@push('scripts')
<script>
function deletePermission(permissionId, permissionName) {
    if (confirm(`{{ __('messages.confirm_delete_permission') }}: ${permissionName}?\n\n{{ __('messages.delete_permission_warning') }}`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/permissions/${permissionId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
