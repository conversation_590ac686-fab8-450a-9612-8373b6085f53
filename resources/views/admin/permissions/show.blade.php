@extends('layouts.admin')

@section('title', __('messages.show_permission'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-key {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.permission_details') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            @can('update permissions')
                <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                    <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit') }}
                </a>
            @endcan
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- Permission Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-key text-white fa-2x"></i>
                    </div>
                    
                    <h4 class="mb-1">{{ $permission->name }}</h4>
                    <p class="text-muted mb-3">{{ __('messages.permission') }}</p>
                    
                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <span class="badge bg-secondary fs-6">{{ $permission->guard_name }}</span>
                        @php
                            $category = explode(' ', $permission->name)[1] ?? 'general';
                        @endphp
                        <span class="badge bg-info fs-6">{{ ucfirst($category) }}</span>
                    </div>

                    @can('update permissions')
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-primary">
                                <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit_permission') }}
                            </a>
                        </div>
                    @endcan
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_stats') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary">{{ $permission->roles->count() }}</h5>
                                <small class="text-muted">{{ __('messages.roles') }}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">{{ $permission->users->count() }}</h5>
                            <small class="text-muted">{{ __('messages.users') }}</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h5 class="text-info">{{ $permission->created_at->format('M d, Y') }}</h5>
                            <small class="text-muted">{{ __('messages.created_at') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.basic_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.permission_name') }}:</td>
                                    <td><code>{{ $permission->name }}</code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.guard_name') }}:</td>
                                    <td><span class="badge bg-secondary">{{ $permission->guard_name }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.category') }}:</td>
                                    <td>
                                        @php
                                            $category = explode(' ', $permission->name)[1] ?? 'general';
                                        @endphp
                                        <span class="badge bg-info">{{ ucfirst($category) }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.created_at') }}:</td>
                                    <td>{{ $permission->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.updated_at') }}:</td>
                                    <td>{{ $permission->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.id') }}:</td>
                                    <td>{{ $permission->id }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles with this Permission -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">{{ __('messages.roles_with_permission') }}</h6>
                        </div>
                        <div class="card-body">
                            @if($permission->roles->count() > 0)
                                @foreach($permission->roles as $role)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="badge bg-primary">{{ $role->name }}</span>
                                            @if($role->description)
                                                <small class="text-muted d-block">{{ $role->description }}</small>
                                            @endif
                                        </div>
                                        @can('read roles')
                                            <a href="{{ route('admin.roles.show', $role) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="{{ __('messages.view_role') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endcan
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted">{{ __('messages.no_roles_with_permission') }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.users_with_permission') }}</h6>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            @if($permission->users->count() > 0)
                                @foreach($permission->users->take(10) as $user)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="d-flex align-items-center">
                                            @if($user->profile_photo_path)
                                                <img src="{{ Storage::url($user->profile_photo_path) }}" 
                                                     alt="{{ $user->name }}" 
                                                     class="rounded-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                                     width="30" height="30">
                                            @else
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                                     style="width: 30px; height: 30px;">
                                                    <i class="fas fa-user text-white small"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $user->name }}</div>
                                                <small class="text-muted">{{ $user->email }}</small>
                                            </div>
                                        </div>
                                        @can('read users')
                                            <a href="{{ route('admin.users.show', $user) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="{{ __('messages.view_user') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endcan
                                    </div>
                                @endforeach
                                @if($permission->users->count() > 10)
                                    <div class="text-center mt-3">
                                        <small class="text-muted">{{ __('messages.and_more_users', ['count' => $permission->users->count() - 10]) }}</small>
                                    </div>
                                @endif
                            @else
                                <p class="text-muted">{{ __('messages.no_users_with_permission') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.usage_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ __('messages.permission_usage') }}</h6>
                            <p class="text-muted small">{{ __('messages.permission_usage_description') }}</p>
                            <div class="d-flex gap-2">
                                <span class="badge bg-primary">{{ $permission->roles->count() }} {{ __('messages.roles') }}</span>
                                <span class="badge bg-success">{{ $permission->users->count() }} {{ __('messages.users') }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('messages.related_permissions') }}</h6>
                            @php
                                $category = explode(' ', $permission->name)[1] ?? 'general';
                                $relatedPermissions = \Spatie\Permission\Models\Permission::where('name', 'like', "%{$category}%")
                                    ->where('id', '!=', $permission->id)
                                    ->limit(5)
                                    ->get();
                            @endphp
                            @if($relatedPermissions->count() > 0)
                                @foreach($relatedPermissions as $related)
                                    <div class="mb-1">
                                        <code class="small">{{ $related->name }}</code>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted small">{{ __('messages.no_related_permissions') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
