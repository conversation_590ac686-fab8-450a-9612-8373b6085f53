@extends('layouts.admin')

@section('title', __('messages.permission_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-key {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.permission_management') }}
        </h1>
        @can('create permissions')
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.permissions.create') }}" class="btn btn-main">
                <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.create_permission') }}
            </a>
        </div>
        @endcan
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.permissions.index') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('messages.search') }}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="{{ __('messages.search_permissions') }}">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">{{ __('messages.category') }}</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">{{ __('messages.all_categories') }}</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                {{ ucfirst(str_replace('_', ' ', $category)) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="guard_name" class="form-label">{{ __('messages.guard') }}</label>
                    <select class="form-select" id="guard_name" name="guard_name">
                        <option value="">{{ __('messages.all_guards') }}</option>
                        <option value="web" {{ request('guard_name') == 'web' ? 'selected' : '' }}>Web</option>
                        <option value="api" {{ request('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.filter') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Permissions Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.permissions') }}</h6>
            <div class="d-flex">
                @can('create permissions')
                    <button class="btn btn-sm btn-outline-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" data-bs-toggle="modal" data-bs-target="#bulkCreateModal">
                        <i class="fas fa-plus-circle {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                        {{ __('messages.bulk_create') }}
                    </button>
                @endcan
                <button class="btn btn-sm btn-outline-secondary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                    {{ __('messages.refresh') }}
                </button>
            </div>
        </div>
        <div class="card-body">
            {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
@can('create permissions')
<div class="modal fade" id="bulkCreateModal" tabindex="-1" aria-labelledby="bulkCreateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.permissions.bulk-create') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkCreateModalLabel">{{ __('messages.bulk_create_permissions') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ __('messages.close') }}"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="resource_name" class="form-label">{{ __('messages.resource_name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="resource_name" name="resource_name" required>
                        <div class="form-text">{{ __('messages.resource_name_help') }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('messages.actions') }} <span class="text-danger">*</span></label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="create" id="action_create" name="actions[]">
                                    <label class="form-check-label" for="action_create">Create</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="read" id="action_read" name="actions[]">
                                    <label class="form-check-label" for="action_read">Read</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="update" id="action_update" name="actions[]">
                                    <label class="form-check-label" for="action_update">Update</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="delete" id="action_delete" name="actions[]">
                                    <label class="form-check-label" for="action_delete">Delete</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="guard_name" class="form-label">{{ __('messages.guard_name') }}</label>
                        <select class="form-select" id="guard_name" name="guard_name">
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('messages.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('messages.create_permissions') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan

<!-- Delete Confirmation Modal -->
@can('delete permissions')
<div class="modal fade" id="deletePermissionModal" tabindex="-1" aria-labelledby="deletePermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="deletePermissionForm" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-header">
                    <h5 class="modal-title" id="deletePermissionModalLabel">{{ __('messages.delete_permission') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ __('messages.close') }}"></button>
                </div>
                <div class="modal-body">
                    <p id="deletePermissionText"></p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                        {{ __('messages.delete_permission_warning') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('messages.cancel') }}</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.delete') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
function deletePermission(permissionId, permissionName) {
    document.getElementById('deletePermissionForm').action = `/admin/permissions/${permissionId}`;
    document.getElementById('deletePermissionText').textContent =
        `{{ __('messages.confirm_delete_permission') }}: ${permissionName}`;
    new bootstrap.Modal(document.getElementById('deletePermissionModal')).show();
}

// Bulk create form validation
document.getElementById('bulkCreateModal').addEventListener('show.bs.modal', function() {
    // Reset form
    document.querySelector('#bulkCreateModal form').reset();
});
</script>
@endpush
