@extends('layouts.admin')

@section('title', __('messages.show_user'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.user_details') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            @can('update users')
                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                    <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit') }}
                </a>
            @endcan
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    @if($user->profile_photo_path)
                        <img src="{{ Storage::url($user->profile_photo_path) }}" 
                             alt="{{ $user->name }}" 
                             class="rounded-circle mb-3" 
                             width="120" height="120">
                    @else
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                             style="width: 120px; height: 120px;">
                            <i class="fas fa-user text-white fa-3x"></i>
                        </div>
                    @endif
                    
                    <h4 class="mb-1">{{ $user->name }}</h4>
                    <p class="text-muted mb-3">{{ $user->email }}</p>
                    
                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }} fs-6">
                            {{ $user->is_active ? __('messages.active') : __('messages.inactive') }}
                        </span>
                        @if($user->email_verified_at)
                            <span class="badge bg-success fs-6">{{ __('messages.verified') }}</span>
                        @else
                            <span class="badge bg-warning fs-6">{{ __('messages.unverified') }}</span>
                        @endif
                    </div>

                    @can('update users')
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                                <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit_user') }}
                            </a>
                            @if(!$user->hasRole('super_admin') && $user->id !== auth()->id())
                                <button type="button" class="btn btn-outline-warning" onclick="toggleUserStatus({{ $user->id }}, '{{ $user->is_active ? 'inactive' : 'active' }}')">
                                    <i class="fas fa-{{ $user->is_active ? 'pause' : 'play' }} {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>
                                    {{ $user->is_active ? __('messages.deactivate') : __('messages.activate') }}
                                </button>
                            @endif
                        </div>
                    @endcan
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_stats') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary">{{ $user->roles->count() }}</h5>
                                <small class="text-muted">{{ __('messages.roles') }}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">{{ $user->getAllPermissions()->count() }}</h5>
                            <small class="text-muted">{{ __('messages.permissions') }}</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h5 class="text-info">{{ $user->activity_count }}</h5>
                            <small class="text-muted">{{ __('messages.activities') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.basic_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.name') }}:</td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.email') }}:</td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.phone') }}:</td>
                                    <td>{{ $user->phone ?? __('messages.not_provided') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.user_type') }}:</td>
                                    <td>{{ $user->type->name ?? __('messages.not_provided') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.language') }}:</td>
                                    <td>{{ $user->lang == 'ar' ? 'العربية' : 'English' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.member_since') }}:</td>
                                    <td>{{ $user->created_at->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.last_login') }}:</td>
                                    <td>{{ $user->last_login ? $user->last_login->created_at->diffForHumans() : __('messages.never') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.status') }}:</td>
                                    <td>
                                        <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                            {{ $user->is_active ? __('messages.active') : __('messages.inactive') }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles and Permissions -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">{{ __('messages.user_roles') }}</h6>
                        </div>
                        <div class="card-body">
                            @if($user->roles->count() > 0)
                                @foreach($user->roles as $role)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="badge bg-primary">{{ $role->name }}</span>
                                            @if($role->description)
                                                <small class="text-muted d-block">{{ $role->description }}</small>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted">{{ __('messages.no_roles_assigned') }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.user_permissions') }}</h6>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            @if($user->getAllPermissions()->count() > 0)
                                @foreach($user->getAllPermissions()->groupBy(function($permission) {
                                    return explode(' ', $permission->name)[1] ?? 'general';
                                }) as $group => $permissions)
                                    <h6 class="text-muted text-uppercase small">{{ ucfirst($group) }}</h6>
                                    @foreach($permissions as $permission)
                                        <span class="badge bg-secondary me-1 mb-1">{{ $permission->name }}</span>
                                    @endforeach
                                    <hr>
                                @endforeach
                            @else
                                <p class="text-muted">{{ __('messages.no_permissions_assigned') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.recent_activity') }}</h6>
                </div>
                <div class="card-body">
                    @if($user->audit_logs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('messages.activity') }}</th>
                                        <th>{{ __('messages.date') }}</th>
                                        <th>{{ __('messages.ip_address') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->audit_logs->take(10) as $log)
                                        <tr>
                                            <td>{{ $log->description }}</td>
                                            <td>{{ $log->created_at->diffForHumans() }}</td>
                                            <td><code>{{ $log->ip_address }}</code></td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">{{ __('messages.no_recent_activity') }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleUserStatus(userId, newStatus) {
    if (confirm('{{ __("messages.confirm_user_status_change") }}')) {
        $.ajax({
            url: `/admin/users/${userId}/toggle-status`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                status: newStatus
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || '{{ __("messages.error_occurred") }}');
                }
            },
            error: function() {
                alert('{{ __("messages.error_occurred") }}');
            }
        });
    }
}
</script>
@endpush
