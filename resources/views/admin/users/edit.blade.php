@extends('layouts.admin')

@section('title', __('messages.edit_user'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-edit {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.edit_user') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-info {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.view') }}
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            <strong>{{ __('messages.validation_errors') }}</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <!-- Edit User Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.user_information') }}</h6>
                    <div class="d-flex align-items-center">
                        @if($user->profile_photo_path)
                            <img src="{{ Storage::url($user->profile_photo_path) }}" 
                                 alt="{{ $user->name }}" 
                                 class="rounded-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                 width="40" height="40">
                        @else
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        @endif
                        <span class="text-muted">{{ $user->name }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.users.update', $user) }}" method="POST" enctype="multipart/form-data" id="editUserForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">{{ __('messages.name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">{{ __('messages.email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">{{ __('messages.phone') }} <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- User Type -->
                            <div class="col-md-6 mb-3">
                                <label for="user_type_id" class="form-label">{{ __('messages.user_type') }} <span class="text-danger">*</span></label>
                                <select class="form-select @error('user_type_id') is-invalid @enderror" 
                                        id="user_type_id" name="user_type_id" required>
                                    <option value="">{{ __('messages.select_option') }}</option>
                                    @foreach($types as $type)
                                        <option value="{{ $type->id }}" {{ old('user_type_id', $user->user_type_id) == $type->id ? 'selected' : '' }}>
                                            {{ $type->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('user_type_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">{{ __('messages.password') }}</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password">
                                <div class="form-text">{{ __('messages.leave_blank_to_keep_current') }}</div>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">{{ __('messages.confirm_password') }}</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation">
                            </div>
                        </div>

                        <!-- Roles -->
                        <div class="mb-3">
                            <label class="form-label">{{ __('messages.user_roles') }} <span class="text-danger">*</span></label>
                            <div class="row">
                                @foreach($roles as $role)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input @error('roles') is-invalid @enderror" 
                                                   type="checkbox" value="{{ $role->id }}" 
                                                   id="role_{{ $role->id }}" name="roles[]"
                                                   {{ in_array($role->id, old('roles', $user->roles->pluck('id')->toArray())) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="role_{{ $role->id }}">
                                                {{ $role->name }}
                                                @if($role->description)
                                                    <small class="text-muted d-block">{{ $role->description }}</small>
                                                @endif
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            @error('roles')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Profile Photo -->
                        <div class="mb-3">
                            <label for="profile_photo" class="form-label">{{ __('messages.profile_photo') }}</label>
                            @if($user->profile_photo_path)
                                <div class="mb-2">
                                    <img src="{{ Storage::url($user->profile_photo_path) }}" 
                                         alt="{{ $user->name }}" 
                                         class="rounded" 
                                         width="100" height="100">
                                </div>
                            @endif
                            <input type="file" class="form-control @error('profile_photo') is-invalid @enderror" 
                                   id="profile_photo" name="profile_photo" accept="image/*">
                            @error('profile_photo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('messages.max_file_size_2mb') }}</div>
                        </div>

                        <!-- Language -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="lang" class="form-label">{{ __('messages.language') }}</label>
                                <select class="form-select @error('lang') is-invalid @enderror" id="lang" name="lang">
                                    <option value="en" {{ old('lang', $user->lang) == 'en' ? 'selected' : '' }}>English</option>
                                    <option value="ar" {{ old('lang', $user->lang) == 'ar' ? 'selected' : '' }}>العربية</option>
                                </select>
                                @error('lang')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        {{ __('messages.active') }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.update') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- User Info Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.user_details') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.member_since') }}:</strong></div>
                        <div class="col-sm-6">{{ $user->created_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.last_login') }}:</strong></div>
                        <div class="col-sm-6">{{ $user->last_login ? $user->last_login->created_at->diffForHumans() : __('messages.never') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.activity_count') }}:</strong></div>
                        <div class="col-sm-6">{{ $user->activity_count }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.status') }}:</strong></div>
                        <div class="col-sm-6">
                            <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                {{ $user->is_active ? __('messages.active') : __('messages.inactive') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.help') }}</h6>
                </div>
                <div class="card-body">
                    <h6>{{ __('messages.password_requirements') }}</h6>
                    <ul class="small text-muted">
                        <li>{{ __('messages.password_min_8_chars') }}</li>
                        <li>{{ __('messages.password_mixed_case') }}</li>
                        <li>{{ __('messages.password_numbers') }}</li>
                        <li>{{ __('messages.password_symbols') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form validation
    $('#editUserForm').on('submit', function(e) {
        const roles = $('input[name="roles[]"]:checked').length;
        if (roles === 0) {
            e.preventDefault();
            alert('{{ __("messages.please_select_at_least_one_role") }}');
            return false;
        }
    });
});
</script>
@endpush
