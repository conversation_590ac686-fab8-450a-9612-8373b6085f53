<div class="btn-group" role="group">
    @can('read users')
        <a href="{{ route('admin.users.show', $id) }}" 
           class="btn btn-sm btn-outline-info" 
           title="{{ __('messages.view_user') }}">
            <i class="fas fa-eye"></i>
        </a>
    @endcan
    
    @can('update users')
        <a href="{{ route('admin.users.edit', $id) }}" 
           class="btn btn-sm btn-outline-primary" 
           title="{{ __('messages.edit_user') }}">
            <i class="fas fa-edit"></i>
        </a>
    @endcan
    
    @can('delete users')
        @if($name !== 'super_admin' && $id !== auth()->id())
            <button type="button" 
                    class="btn btn-sm btn-outline-danger" 
                    onclick="deleteUser({{ $id }}, '{{ $name }}')"
                    title="{{ __('messages.delete_user') }}">
                <i class="fas fa-trash"></i>
            </button>
        @endif
    @endcan
    
    @can('update users')
        @if(!in_array($name, ['super_admin']) && $id !== auth()->id())
            <button type="button"
                    class="btn btn-sm btn-outline-{{ $is_active ? 'warning' : 'success' }}"
                    onclick="toggleUserStatus({{ $id }}, '{{ $name }}', {{ $is_active ? 'false' : 'true' }})"
                    title="{{ $is_active ? __('messages.deactivate') : __('messages.activate') }}">
                <i class="fas fa-{{ $is_active ? 'user-slash' : 'user-check' }}"></i>
            </button>
        @endif
    @endcan
</div>

@push('scripts')
<script>
function deleteUser(userId, userName) {
    if (confirm(`{{ __('messages.are_you_sure') }} {{ __('messages.delete_user') }}: ${userName}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleUserStatus(userId, userName, newStatus) {
    const action = newStatus === 'true' ? '{{ __('messages.activate') }}' : '{{ __('messages.deactivate') }}';
    
    if (confirm(`{{ __('messages.are_you_sure') }} ${action}: ${userName}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}/toggle-status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const statusField = document.createElement('input');
        statusField.type = 'hidden';
        statusField.name = 'is_active';
        statusField.value = newStatus;
        
        form.appendChild(csrfToken);
        form.appendChild(statusField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
