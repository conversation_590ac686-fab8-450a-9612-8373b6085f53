@extends('layouts.admin')

@section('title', __('messages.edit_role'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-shield {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.edit_role') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-outline-info {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.view') }}
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            <strong>{{ __('messages.validation_errors') }}</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- Edit Role Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.role_information') }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.roles.update', $role) }}" method="POST" id="editRoleForm">
                        @csrf
                        @method('PUT')
                        
                        <!-- Role Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('messages.role_name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $role->name) }}" required
                                   {{ $role->name === 'super_admin' && !auth()->user()->hasRole('super_admin') ? 'readonly' : '' }}>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Role Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('messages.role_description') }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $role->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Permissions -->
                        <div class="mb-4">
                            <label class="form-label">{{ __('messages.role_permissions') }}</label>
                            
                            @if($role->name !== 'super_admin')
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all-permissions">
                                            <label class="form-check-label fw-bold" for="select-all-permissions">
                                                {{ __('messages.select_all_permissions') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                @foreach($permissionCategories as $category => $categoryPermissions)
                                    <div class="card mb-3">
                                        <div class="card-header py-2">
                                            <div class="form-check">
                                                <input class="form-check-input category-checkbox" type="checkbox" 
                                                       id="category_{{ $category }}" data-category="{{ $category }}">
                                                <label class="form-check-label fw-bold" for="category_{{ $category }}">
                                                    {{ __(ucfirst(str_replace('_', ' ', $category))) }}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row">
                                                @foreach($categoryPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox @error('permissions') is-invalid @enderror" 
                                                                   type="checkbox" value="{{ $permission->id }}" 
                                                                   id="permission_{{ $permission->id }}" name="permissions[]"
                                                                   data-category="{{ $category }}"
                                                                   {{ in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray())) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                {{ $permission->name }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                                    {{ __('messages.super_admin_has_all_permissions') }}
                                </div>
                            @endif

                            @error('permissions')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.cancel') }}
                            </a>
                            @if($role->name !== 'super_admin' || auth()->user()->hasRole('super_admin'))
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.update') }}
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Role Info Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('messages.role_details') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.role_name') }}:</strong></div>
                        <div class="col-sm-6">{{ $role->name }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.created_at') }}:</strong></div>
                        <div class="col-sm-6">{{ $role->created_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.users_count') }}:</strong></div>
                        <div class="col-sm-6">{{ $role->users->count() }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-6"><strong>{{ __('messages.permissions_count') }}:</strong></div>
                        <div class="col-sm-6">{{ $role->permissions->count() }}</div>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.help') }}</h6>
                </div>
                <div class="card-body">
                    <h6>{{ __('messages.role_management_tips') }}</h6>
                    <ul class="small text-muted">
                        <li>{{ __('messages.role_tip_1') }}</li>
                        <li>{{ __('messages.role_tip_2') }}</li>
                        <li>{{ __('messages.role_tip_3') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Select all permissions functionality
    $('#select-all-permissions').on('change', function() {
        $('.permission-checkbox').prop('checked', $(this).is(':checked'));
        updateCategoryCheckboxes();
    });
    
    // Category checkbox functionality
    $('.category-checkbox').on('change', function() {
        const category = $(this).data('category');
        const isChecked = $(this).is(':checked');
        $(`.permission-checkbox[data-category="${category}"]`).prop('checked', isChecked);
        updateSelectAllCheckbox();
    });
    
    // Individual permission checkbox functionality
    $('.permission-checkbox').on('change', function() {
        updateCategoryCheckboxes();
        updateSelectAllCheckbox();
    });
    
    // Initialize checkbox states
    updateCategoryCheckboxes();
    updateSelectAllCheckbox();
    
    function updateCategoryCheckboxes() {
        $('.category-checkbox').each(function() {
            const category = $(this).data('category');
            const categoryPermissions = $(`.permission-checkbox[data-category="${category}"]`);
            const checkedPermissions = $(`.permission-checkbox[data-category="${category}"]:checked`);
            
            if (checkedPermissions.length === categoryPermissions.length) {
                $(this).prop('checked', true).prop('indeterminate', false);
            } else if (checkedPermissions.length > 0) {
                $(this).prop('checked', false).prop('indeterminate', true);
            } else {
                $(this).prop('checked', false).prop('indeterminate', false);
            }
        });
    }
    
    function updateSelectAllCheckbox() {
        const totalPermissions = $('.permission-checkbox').length;
        const checkedPermissions = $('.permission-checkbox:checked').length;
        
        if (checkedPermissions === totalPermissions) {
            $('#select-all-permissions').prop('checked', true).prop('indeterminate', false);
        } else if (checkedPermissions > 0) {
            $('#select-all-permissions').prop('checked', false).prop('indeterminate', true);
        } else {
            $('#select-all-permissions').prop('checked', false).prop('indeterminate', false);
        }
    }
});
</script>
@endpush
