<div class="btn-group" role="group">
    @can('read roles')
        <a href="{{ route('admin.roles.show', $id) }}" 
           class="btn btn-sm btn-outline-info" 
           title="{{ __('messages.view_role') }}">
            <i class="fas fa-eye"></i>
        </a>
    @endcan
    
    @can('update roles')
        @if($name !== 'super_admin' || auth()->user()->hasRole('super_admin'))
            <a href="{{ route('admin.roles.edit', $id) }}" 
               class="btn btn-sm btn-outline-primary" 
               title="{{ __('messages.edit_role') }}">
                <i class="fas fa-edit"></i>
            </a>
        @endif
    @endcan
    
    @can('create roles')
        <button type="button" 
                class="btn btn-sm btn-outline-secondary" 
                onclick="cloneRole({{ $id }}, '{{ $name }}')"
                title="{{ __('messages.clone_role') }}">
            <i class="fas fa-copy"></i>
        </button>
    @endcan
    
    @can('delete roles')
        @if($name !== 'super_admin')
            <button type="button" 
                    class="btn btn-sm btn-outline-danger" 
                    onclick="deleteRole({{ $id }}, '{{ $name }}')"
                    title="{{ __('messages.delete_role') }}">
                <i class="fas fa-trash"></i>
            </button>
        @endif
    @endcan
</div>

@push('scripts')
<script>
function cloneRole(roleId, roleName) {
    const newName = prompt(`{{ __('messages.new_role_name') }}:`, roleName + '_copy');
    
    if (newName && newName.trim() !== '') {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/roles/${roleId}/clone`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const nameField = document.createElement('input');
        nameField.type = 'hidden';
        nameField.name = 'name';
        nameField.value = newName.trim();
        
        form.appendChild(csrfToken);
        form.appendChild(nameField);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteRole(roleId, roleName) {
    if (confirm(`{{ __('messages.confirm_delete_role') }}: ${roleName}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/roles/${roleId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
