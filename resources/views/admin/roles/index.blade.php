@extends('layouts.admin')

@section('title', __('messages.role_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-shield {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.role_management') }}
        </h1>
        @can('create roles')
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('admin.roles.create') }}" class="btn btn-main">
                <i class="fas fa-plus {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.create_role') }}
            </a>
        </div>
        @endcan
    </div>

    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.total_roles') }}</h6>
                            <h3 class="mb-0 fw-bold text-primary">{{ $permissionStats['total_roles'] }}</h3>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-user-shield fa-2x"></i>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.total_permissions') }}</h6>
                            <h3 class="mb-0 fw-bold text-success">{{ $permissionStats['total_permissions'] }}</h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-muted mb-2">{{ __('messages.users_with_roles') }}</h6>
                            <h3 class="mb-0 fw-bold text-info">{{ $permissionStats['users_with_roles'] }}</h3>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.roles.index') }}" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">{{ __('messages.search') }}</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ $search }}" placeholder="{{ __('messages.search_roles') }}">
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>{{ __('messages.search') }}
                            </button>
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>{{ __('messages.clear') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Roles Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __('messages.roles_list') }}</h5>
                </div>
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
                </div>
    </div>
</div>

<!-- Clone Role Modal -->
<div class="modal fade" id="cloneRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('messages.clone_role') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="cloneRoleForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="cloneRoleName" class="form-label">{{ __('messages.new_role_name') }}</label>
                        <input type="text" class="form-control" id="cloneRoleName" name="name" required>
                    </div>
                    <p class="text-muted">{{ __('messages.clone_role_description') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('messages.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-copy me-1"></i>{{ __('messages.clone_role') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Role Modal -->
<div class="modal fade" id="deleteRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">{{ __('messages.delete_role') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="deleteRoleForm" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ __('messages.delete_role_warning') }}
                    </div>
                    <p id="deleteRoleText"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('messages.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>{{ __('messages.delete') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
{!! $dataTable->scripts() !!}

<script>
function cloneRole(roleId, roleName) {
    document.getElementById('cloneRoleForm').action = `/admin/roles/${roleId}/clone`;
    document.getElementById('cloneRoleName').value = roleName + '_copy';
    new bootstrap.Modal(document.getElementById('cloneRoleModal')).show();
}

function deleteRole(roleId, roleName) {
    document.getElementById('deleteRoleForm').action = `/admin/roles/${roleId}`;
    document.getElementById('deleteRoleText').textContent =
        `{{ __('messages.confirm_delete_role') }}: ${roleName}`;
    new bootstrap.Modal(document.getElementById('deleteRoleModal')).show();
}
</script>
@endpush
