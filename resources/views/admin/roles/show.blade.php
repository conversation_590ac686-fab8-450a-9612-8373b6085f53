@extends('layouts.admin')

@section('title', __('messages.show_role'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-user-shield {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>{{ __('messages.role_details') }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            @can('update roles')
                @if($role->name !== 'super_admin' || auth()->user()->hasRole('super_admin'))
                    <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}">
                        <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit') }}
                    </a>
                @endif
            @endcan
            <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{{ __('messages.close') }}"></button>
        </div>
    @endif

    <div class="row">
        <!-- Role Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user-shield text-white fa-2x"></i>
                    </div>
                    
                    <h4 class="mb-1">{{ $role->name }}</h4>
                    @if($role->description)
                        <p class="text-muted mb-3">{{ $role->description }}</p>
                    @endif
                    
                    <div class="d-flex justify-content-center gap-2 mb-3">
                        @if($role->name === 'super_admin')
                            <span class="badge bg-danger fs-6">{{ __('messages.system_role') }}</span>
                        @else
                            <span class="badge bg-primary fs-6">{{ __('messages.custom_role') }}</span>
                        @endif
                    </div>

                    @can('update roles')
                        @if($role->name !== 'super_admin' || auth()->user()->hasRole('super_admin'))
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary">
                                    <i class="fas fa-edit {{ app()->getLocale() == 'ar' ? 'ms-1' : 'me-1' }}"></i>{{ __('messages.edit_role') }}
                                </a>
                            </div>
                        @endif
                    @endcan
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_stats') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary">{{ $role->users->count() }}</h5>
                                <small class="text-muted">{{ __('messages.users') }}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">{{ $role->permissions->count() }}</h5>
                            <small class="text-muted">{{ __('messages.permissions') }}</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h5 class="text-info">{{ $role->created_at->format('M d, Y') }}</h5>
                            <small class="text-muted">{{ __('messages.created_at') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.basic_information') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.role_name') }}:</td>
                                    <td>{{ $role->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.guard_name') }}:</td>
                                    <td>{{ $role->guard_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.created_at') }}:</td>
                                    <td>{{ $role->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{{ __('messages.role_description') }}:</td>
                                    <td>{{ $role->description ?? __('messages.not_provided') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.updated_at') }}:</td>
                                    <td>{{ $role->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{{ __('messages.users_count') }}:</td>
                                    <td>{{ $role->users->count() }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">{{ __('messages.role_permissions') }}</h6>
                </div>
                <div class="card-body">
                    @if($role->name === 'super_admin')
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}"></i>
                            {{ __('messages.super_admin_has_all_permissions') }}
                        </div>
                    @elseif($role->permissions->count() > 0)
                        @foreach($role->permissions->groupBy(function($permission) {
                            return explode(' ', $permission->name)[1] ?? 'general';
                        }) as $group => $permissions)
                            <div class="mb-3">
                                <h6 class="text-muted text-uppercase small border-bottom pb-1">{{ ucfirst($group) }}</h6>
                                <div class="row">
                                    @foreach($permissions as $permission)
                                        <div class="col-md-4 mb-2">
                                            <span class="badge bg-secondary">{{ $permission->name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">{{ __('messages.no_permissions_assigned') }}</p>
                    @endif
                </div>
            </div>

            <!-- Users with this Role -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">{{ __('messages.users_with_role') }}</h6>
                </div>
                <div class="card-body">
                    @if($role->users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('messages.name') }}</th>
                                        <th>{{ __('messages.email') }}</th>
                                        <th>{{ __('messages.status') }}</th>
                                        <th>{{ __('messages.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($role->users->take(10) as $user)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($user->profile_photo_path)
                                                        <img src="{{ Storage::url($user->profile_photo_path) }}" 
                                                             alt="{{ $user->name }}" 
                                                             class="rounded-circle {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                                             width="30" height="30">
                                                    @else
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center {{ app()->getLocale() == 'ar' ? 'ms-2' : 'me-2' }}" 
                                                             style="width: 30px; height: 30px;">
                                                            <i class="fas fa-user text-white small"></i>
                                                        </div>
                                                    @endif
                                                    {{ $user->name }}
                                                </div>
                                            </td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                                    {{ $user->is_active ? __('messages.active') : __('messages.inactive') }}
                                                </span>
                                            </td>
                                            <td>
                                                @can('read users')
                                                    <a href="{{ route('admin.users.show', $user) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="{{ __('messages.view_user') }}">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcan
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($role->users->count() > 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.users.index', ['role' => $role->name]) }}" class="btn btn-outline-primary">
                                    {{ __('messages.view_all_users') }} ({{ $role->users->count() }})
                                </a>
                            </div>
                        @endif
                    @else
                        <p class="text-muted">{{ __('messages.no_users_with_role') }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
