<?php

return [
    'client_form'                => 'Client Registration Form',
    'client_list'                => 'Client List',
    'call_center'                => 'Customer Service Center',
    'form_has_errors'            => 'There are errors in your submission!',
    'please_check_fields'        => 'Please check the highlighted fields and correct them.',
    'fields_marked_required'     => 'Fields marked with * are required',
    'student_name'               => 'Student Name',
    'parent_information'         => 'Parent Information',
    'father_job'                 => 'Job of Guardian (Father)',
    'mother_job'                 => 'Job of Guardian (Mother)',
    'home_phone'                 => 'Home Phone Number',
    'mother_phone'               => 'Mother\'s Phone Number',
    'father_phone'               => 'Father\'s Phone Number',
    'extra_phone'                => 'Additional Phone Number',
    'contact_priority'           => 'Communication Priority',
    'contact_father'             => 'Father',
    'contact_mother'             => 'Mother',
    'address'                    => 'Detailed Address',
    'location'                   => 'Location',
    'area'                       => 'Area',
    'school'                     => 'School',
    'education_department'       => 'Educational Department',
    'national'                   => 'National',
    'international'              => 'Inter National',
    'igsec'                      => 'IGSEC',
    'french'                     => 'French',
    'american'                   => 'American Diploma',
    'dutch'                      => 'Special Cases',
    'education_stage'            => 'Educational Level',
    'class_level'                => 'Class Level',
    'kg'                         => 'KG',
    'middle'                     => 'Middle',
    'junior'                     => 'Junior',
    'senior'                     => 'Senior',
    'primary'                    => 'Primary',
    'secondary'                  => 'Secondary',
    'preparatory'                => 'Preparatory',
    'entry_time'                 => 'Entry Time',
    'exit_time'                  => 'Check Out Time',
    'car_type'                   => 'Type of Car',
    'qasrawy'                    => 'Qasrawy',
    'high_roof'                  => 'High Roof',
    'private7'                   => 'Private (7 Seats)',
    'private_classic'            => 'Private Car',
    'coaster'                    => 'Coaster (21 Seats)',
    'chevrolet'                  => 'Chevrolet',
    'study_start_date'           => 'Study Start Date',

    // Common UI Elements
    'dashboard' => 'Dashboard',
    'users' => 'Users',
    'roles' => 'Roles',
    'permissions' => 'Permissions',
    'clients' => 'Clients',
    'routes' => 'Routes',
    'drivers' => 'Drivers',
    'subscriptions' => 'Subscriptions',
    'payments' => 'Payments',
    'students' => 'Students',
    'settings' => 'Settings',
    'profile' => 'Profile',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',
    'email' => 'Email',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'name' => 'Name',
    'phone' => 'Phone',
    'address' => 'Address',
    'status' => 'Status',
    'actions' => 'Actions',
    'create' => 'Create',
    'edit' => 'Edit',
    'update' => 'Update',
    'delete' => 'Delete',
    'view' => 'View',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'submit' => 'Submit',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'refresh' => 'Refresh',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'first' => 'First',
    'last' => 'Last',
    'showing' => 'Showing',
    'of' => 'of',
    'entries' => 'entries',
    'no_data' => 'No data available',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'confirmation' => 'Confirmation',
    'are_you_sure' => 'Are you sure?',
    'yes' => 'Yes',
    'no' => 'No',
    'ok' => 'OK',
    'close' => 'Close',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'created_by' => 'Created By',
    'updated_by' => 'Updated By',
    'date' => 'Date',
    'time' => 'Time',
    'datetime' => 'Date & Time',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'amount' => 'Amount',
    'price' => 'Price',
    'quantity' => 'Quantity',
    'description' => 'Description',
    'notes' => 'Notes',
    'comments' => 'Comments',
    'type' => 'Type',
    'category' => 'Category',
    'title' => 'Title',
    'content' => 'Content',
    'image' => 'Image',
    'file' => 'File',
    'upload' => 'Upload',
    'download' => 'Download',
    'required' => 'Required',
    'optional' => 'Optional',
    'select' => 'Select',
    'choose' => 'Choose',
    'browse' => 'Browse',
    'clear' => 'Clear',
    'reset' => 'Reset',
    'apply' => 'Apply',
    'confirm' => 'Confirm',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'draft' => 'Draft',
    'published' => 'Published',
    'archived' => 'Archived',
    'deleted' => 'Deleted',
    'restored' => 'Restored',
    'language' => 'Language',
    'english' => 'English',
    'arabic' => 'Arabic',
    'change_language' => 'Change Language',
    'welcome' => 'Welcome',
    'hello' => 'Hello',
    'goodbye' => 'Goodbye',
    'thank_you' => 'Thank You',
    'please' => 'Please',
    'sorry' => 'Sorry',
    'excuse_me' => 'Excuse Me',
    'help' => 'Help',
    'support' => 'Support',
    'contact' => 'Contact',
    'about' => 'About',
    'privacy' => 'Privacy',
    'terms' => 'Terms',
    'copyright' => 'Copyright',
    'all_rights_reserved' => 'All Rights Reserved',
    'basic_information'          => 'Basic Information',
    'update_client'              => 'Update Client',
    'no_expiring_licenses'       => 'No drivers have licenses expiring within the next 30 days.',
    'drivers_with_expiring_licenses' => 'Drivers with Expiring Licenses',
    'days_remaining'             => 'Days Remaining',
    'days'                       => 'days',
    'no_route_assigned'          => 'No route assigned',
    'subscription_type'          => 'Subscription Type',
    'subscription_one_time'      => 'Annual Subscription (One Payment)',
    'subscription_monthly'       => 'Annual Subscription (Monthly Installments)',
    'client_type'                => 'Client Type',
    'new_client'                 => 'New Client',
    'old_client'                 => 'Previous Client',
    'comments'                   => 'Comments',
    'submit'                     => 'Submit',
    'created_at'                 => 'Created At',
    'form_received_successfully' => 'Form submitted successfully!',
    'back_to_form'               => 'Register New Client',
    'students'                   => 'Students',
    'add_student'                => 'Add Student',
    'remove_student'             => 'Remove Student',
    'student_number'             => 'Student #',
    'multiple_students_info'     => 'You can add multiple students in the same form',
    'user_account_created'       => 'User account created successfully',
    'default_password_info'      => 'A user account has been created for you with a default password. Account details will be sent via phone.',
    'other_school'               => 'Other School',
    'client_registration'        => 'Client Registration',
    'royal_transit'              => 'Royal Transit',
    'english'                    => 'English',
    'arabic'                     => 'العربية',
    'geolocation_not_supported'  => 'Geolocation is not supported in this browser.',
    'location_access_denied'     => 'Could not get location. Please make sure you allowed location access.',

    // Admin Panel Translations
    'admin_panel'                => 'Admin Panel',
    'dashboard'                  => 'Dashboard',
    'clients'                    => 'Clients',
    'routes'                     => 'Routes',
    'drivers'                    => 'Drivers',
    'financial'                  => 'Financial',
    'reports'                    => 'Reports',
    'settings'                   => 'Settings',
    'logout'                     => 'Logout',

    // Client Management
    'client_management'          => 'Client Management',
    'add_client'                 => 'Add Client',
    'edit_client'                => 'Edit Client',
    'view_client'                => 'View Client',
    'delete_client'              => 'Delete Client',
    'client_details'             => 'Client Details',
    'total_clients'              => 'Total Clients',
    'active_clients'             => 'Active Clients',
    'pending_clients'            => 'Pending Clients',

    // Route Management
    'route_management'           => 'Route Management',
    'add_route'                  => 'Add Route',
    'edit_route'                 => 'Edit Route',
    'view_route'                 => 'View Route',
    'delete_route'               => 'Delete Route',
    'route_details'              => 'Route Details',
    'total_routes'               => 'Total Routes',
    'active_routes'              => 'Active Routes',
    'route_capacity'             => 'Route Capacity',
    'from_area'                  => 'From Area',
    'to_school'                  => 'To School',
    'pickup_time'                => 'Pickup Time',
    'dropoff_time'               => 'Dropoff Time',
    'current_students'           => 'Current Students',
    'routes_list'                => 'Routes List',
    'total_capacity'             => 'Total Capacity',
    'capacity_report'            => 'Capacity Report',
    'driver_assignments'         => 'Driver Assignments',
    'schedule_overview'          => 'Schedule Overview',
    'export_routes'              => 'Export Routes',
    'route_capacity_report'      => 'Route Capacity Report',
    'capacity'                   => 'Capacity',
    'route_stops'                => 'Route Stops',
    'add_stop'                   => 'Add Stop',
    'remove_stop'                => 'Remove Stop',
    'stop_name'                  => 'Stop Name',
    'stop_time'                  => 'Stop Time',
    'assigned_driver'            => 'Assigned Driver',
    'no_driver_assigned'         => 'No Driver Assigned',

    // Driver Management
    'driver_management'          => 'Driver Management',
    'add_driver'                 => 'Add Driver',
    'edit_driver'                => 'Edit Driver',
    'view_driver'                => 'View Driver',
    'delete_driver'              => 'Delete Driver',
    'driver_details'             => 'Driver Details',
    'total_drivers'              => 'Total Drivers',
    'active_drivers'             => 'Active Drivers',
    'assigned_drivers'           => 'Assigned to Routes',
    'expiring_licenses'          => 'Expiring Licenses',
    'driver_name'                => 'Driver Name',
    'phone_number'               => 'Phone Number',
    'email_address'              => 'Email Address',
    'national_id'                => 'National ID',
    'license_number'             => 'License Number',
    'license_expiry'             => 'License Expiry',
    'hire_date'                  => 'Hire Date',
    'monthly_salary'             => 'Monthly Salary',
    'driver_status'              => 'Driver Status',
    'active'                     => 'Active',
    'inactive'                   => 'Inactive',
    'suspended'                  => 'Suspended',
    'vehicle_information'        => 'Vehicle Information',
    'vehicle_type'               => 'Vehicle Type',
    'vehicle_capacity'           => 'Vehicle Capacity',
    'vehicle_model'              => 'Vehicle Model',
    'vehicle_year'               => 'Vehicle Year',
    'plate_number'               => 'Plate Number',
    'vehicle_color'              => 'Vehicle Color',
    'vehicle_notes'              => 'Vehicle Notes',
    'emergency_contact'          => 'Emergency Contact',
    'contact_name'               => 'Contact Name',
    'contact_phone'              => 'Contact Phone',
    'relationship'               => 'Relationship',
    'personal_information'       => 'Personal Information',
    'license_employment'         => 'License & Employment',
    'additional_notes'           => 'Additional Notes',
    'current_assignment'         => 'Current Assignment',
    'route_assignment_history'   => 'Route Assignment History',
    'driver_statistics'          => 'Driver Statistics',
    'years_of_service'           => 'Years of Service',
    'license_status'             => 'License Status',
    'valid'                      => 'Valid',
    'expired'                    => 'Expired',
    'expiring_soon'              => 'Expiring Soon',
    'suspend_driver'             => 'Suspend Driver',
    'activate_driver'            => 'Activate Driver',
    'remove_from_route'          => 'Remove from Route',
    'assign_to_route'            => 'Assign to Route',

    // Financial Management
    'financial_management'       => 'Financial Management',
    'total_revenue'              => 'Total Revenue',
    'monthly_revenue'            => 'Monthly Revenue',
    'pending_payments'           => 'Pending Payments',
    'payment_history'            => 'Payment History',
    'subscription_revenue'       => 'Subscription Revenue',
    'payment_status'             => 'Payment Status',
    'paid'                       => 'Paid',
    'pending'                    => 'Pending',
    'overdue'                    => 'Overdue',

    // Common Actions
    'actions'                    => 'Actions',
    'create'                     => 'Create',
    'edit'                       => 'Edit',
    'view'                       => 'View',
    'delete'                     => 'Delete',
    'save'                       => 'Save',
    'cancel'                     => 'Cancel',
    'update'                     => 'Update',
    'back'                       => 'Back',
    'search'                     => 'Search',
    'filter'                     => 'Filter',
    'export'                     => 'Export',
    'import'                     => 'Import',
    'refresh'                    => 'Refresh',
    'loading'                    => 'Loading...',
    'no_data'                    => 'No data available',
    'confirm_delete'             => 'Are you sure you want to delete this item?',
    'success'                    => 'Success',
    'error'                      => 'Error',
    'warning'                    => 'Warning',
    'info'                       => 'Information',

    // Form Validation
    'required_field'             => 'This field is required',
    'invalid_email'              => 'Please enter a valid email address',
    'invalid_phone'              => 'Please enter a valid phone number',
    'invalid_date'               => 'Please enter a valid date',
    'min_length'                 => 'Minimum length is :min characters',
    'max_length'                 => 'Maximum length is :max characters',

    // Status Messages
    'created_successfully'       => 'Created successfully',
    'updated_successfully'       => 'Updated successfully',
    'deleted_successfully'       => 'Deleted successfully',
    'operation_failed'           => 'Operation failed',
    'permission_denied'          => 'Permission denied',
    'not_found'                  => 'Item not found',

    // Dashboard translations
    'dashboard'                  => 'Dashboard',
    'export'                     => 'Export',
    'refresh'                    => 'Refresh',
    'view_all'                   => 'View All',
    'total_clients'              => 'Total Clients',
    'this_month'                 => 'This Month',
    'active_routes'              => 'Active Routes',
    'total'                      => 'Total',
    'active_drivers'             => 'Active Drivers',
    'on_leave'                   => 'On Leave',
    'monthly_revenue'            => 'Monthly Revenue',
    'vs_last_month'              => 'vs Last Month',
    'view_financial'             => 'View Financial',
    'total_students'             => 'Total Students',
    'active'                     => 'Active',
    'active_subscriptions'       => 'Active Subscriptions',
    'pending'                    => 'Pending',
    'pending_payments'           => 'Pending Payments',
    'overdue'                    => 'Overdue',
    'total_revenue'              => 'Total Revenue',
    'all_time'                   => 'All Time',
    'recent_subscriptions'       => 'Recent Subscriptions',
    'client'                     => 'Client',
    'route'                      => 'Route',
    'type'                       => 'Type',
    'status'                     => 'Status',
    'created'                    => 'Created',
    'no_recent_subscriptions'    => 'No recent subscriptions',
    'recent_payments'            => 'Recent Payments',
    'no_recent_payments'         => 'No recent payments',
    'quick_actions'              => 'Quick Actions',
    'add_client'                 => 'Add Client',
    'add_route'                  => 'Add Route',
    'add_driver'                 => 'Add Driver',
    'view_reports'               => 'View Reports',
    'manage_routes'              => 'Manage Routes',
    'manage_drivers'             => 'Manage Drivers',
    'completed'                  => 'Completed',
    'monthly'                    => 'Monthly',
    'yearly'                     => 'Yearly',

    // Audit logging messages
    'admin_client_created'       => 'Admin created new client',
    'admin_client_updated'       => 'Admin updated client information',
    'admin_client_deleted'       => 'Admin deleted client',
    'admin_client_viewed'        => 'Admin viewed client details',
    'admin_route_created'        => 'Admin created new route',
    'admin_route_updated'        => 'Admin updated route information',
    'admin_route_deleted'        => 'Admin deleted route',
    'admin_route_viewed'         => 'Admin viewed route details',
    'admin_driver_created'       => 'Admin created new driver',
    'admin_driver_updated'       => 'Admin updated driver information',
    'admin_driver_deleted'       => 'Admin deleted driver',
    'admin_driver_viewed'        => 'Admin viewed driver details',
    'admin_clients_list_viewed'  => 'Admin accessed clients list',
    'admin_routes_list_viewed'   => 'Admin accessed routes list',
    'admin_drivers_list_viewed'  => 'Admin accessed drivers list',

    // Audit Log Types
    'log_type_1'                 => 'Create',
    'log_type_2'                 => 'Update',
    'log_type_3'                 => 'Delete',
    'log_type_4'                 => 'View',
    'log_type_5'                 => 'Export',
    'log_type_6'                 => 'Import',
    'log_type_7'                 => 'Other',

    // Payment Messages
    'payment_created_successfully' => 'Payment created successfully',
    'payment_updated_successfully' => 'Payment updated successfully',
    'payment_deleted_successfully' => 'Payment deleted successfully',
    'payment_status_updated_successfully' => 'Payment status updated successfully',
    'invoice_generated_successfully' => 'Invoice generated successfully',
    'payments_exported_successfully' => 'Payments exported successfully',
    'bulk_status_updated_successfully' => 'Bulk status updated successfully',
    'cannot_delete_completed_payment' => 'Cannot delete completed payment',
    'cannot_modify_completed_payment' => 'Cannot modify completed payment',
    'subscription_required' => 'Subscription is required',
    'subscription_not_found' => 'Subscription not found',
    'amount_required' => 'Amount is required',
    'amount_numeric' => 'Amount must be a number',
    'amount_min' => 'Amount must be at least 0',
    'amount_max' => 'Amount cannot exceed 999,999.99',
    'amount_exceeds_due' => 'Amount (:amount) exceeds due amount (:due)',
    'payment_date_required' => 'Payment date is required',
    'payment_date_date' => 'Payment date must be a valid date',
    'payment_date_past' => 'Payment date cannot be in the future',
    'payment_method_required' => 'Payment method is required',
    'payment_method_invalid' => 'Payment method must be cash, card, or bank_transfer',
    'payment_status_required' => 'Payment status is required',
    'payment_status_invalid' => 'Payment status must be pending, completed, or failed',
    'reference_number_exists' => 'Reference number already exists',
    'reference_number_max_length' => 'Reference number cannot exceed 255 characters',

    // Subscription Messages
    'subscription_created_successfully' => 'Subscription created successfully',
    'subscription_updated_successfully' => 'Subscription updated successfully',
    'subscription_deleted_successfully' => 'Subscription deleted successfully',
    'cannot_delete_subscription_with_payments' => 'Cannot delete subscription with payments',
    'client_required' => 'Client is required',
    'client_not_found' => 'Client not found',
    'route_required' => 'Route is required',
    'route_not_found' => 'Route not found',
    'route_at_capacity' => 'Route is at full capacity',
    'student_name_required' => 'Student name is required',
    'student_name_max_length' => 'Student name cannot exceed 255 characters',
    'subscription_type_required' => 'Subscription type is required',
    'subscription_type_invalid' => 'Subscription type must be monthly, term, or yearly',
    'price_required' => 'Price is required',
    'price_numeric' => 'Price must be a number',
    'price_min' => 'Price must be at least 0',
    'price_max' => 'Price cannot exceed 999,999.99',
    'price_mismatch' => 'Price (:price) does not match route price (:route_price)',
    'start_date_required' => 'Start date is required',
    'start_date_date' => 'Start date must be a valid date',
    'end_date_required' => 'End date is required',
    'end_date_date' => 'End date must be a valid date',
    'end_date_after_start' => 'End date must be after start date',
    'subscription_status_required' => 'Subscription status is required',
    'subscription_status_invalid' => 'Subscription status must be active, inactive, or suspended',
    'subscription_overlap' => 'Client already has an active subscription for this period',
    'notes_max_length' => 'Notes cannot exceed 1000 characters',

    // Route Messages
    'route_created_successfully' => 'Route created successfully',
    'route_updated_successfully' => 'Route updated successfully',
    'route_deleted_successfully' => 'Route deleted successfully',
    'cannot_delete_route_with_subscriptions' => 'Cannot delete route with active subscriptions',
    'cannot_deactivate_route_with_active_subscriptions' => 'Cannot deactivate route with active subscriptions',
    'route_name_required' => 'Route name is required',
    'route_name_exists' => 'Route name already exists',
    'route_name_max_length' => 'Route name cannot exceed 255 characters',
    'from_area_required' => 'From area is required',
    'from_area_max_length' => 'From area cannot exceed 255 characters',
    'to_school_required' => 'To school is required',
    'to_school_max_length' => 'To school cannot exceed 255 characters',
    'pickup_time_required' => 'Pickup time is required',
    'pickup_time_format' => 'Pickup time must be in HH:MM format',
    'dropoff_time_required' => 'Dropoff time is required',
    'dropoff_time_format' => 'Dropoff time must be in HH:MM format',
    'dropoff_time_after_pickup' => 'Dropoff time must be after pickup time',
    'monthly_price_required' => 'Monthly price is required',
    'monthly_price_numeric' => 'Monthly price must be a number',
    'monthly_price_min' => 'Monthly price must be at least 0',
    'monthly_price_max' => 'Monthly price cannot exceed 999,999.99',
    'capacity_required' => 'Capacity is required',
    'capacity_integer' => 'Capacity must be a whole number',
    'capacity_min' => 'Capacity must be at least 1',
    'capacity_max' => 'Capacity cannot exceed 100',
    'capacity_below_current_students' => 'Capacity (:requested) cannot be less than current students (:current)',
    'driver_not_active' => 'Selected driver is not active',
    'driver_already_assigned' => 'Driver is already assigned to another route',
    'stops_array' => 'Stops must be an array',
    'stops_max_count' => 'Cannot have more than 20 stops',
    'stop_string' => 'Each stop must be text',
    'stop_max_length' => 'Each stop cannot exceed 255 characters',

    // Driver Messages
    'driver_created_successfully' => 'Driver created successfully',
    'driver_updated_successfully' => 'Driver updated successfully',
    'driver_deleted_successfully' => 'Driver deleted successfully',
    'cannot_delete_driver_with_active_routes' => 'Cannot delete driver with active route assignments',
    'cannot_deactivate_driver_with_active_routes' => 'Cannot deactivate driver with active route assignments',
    'driver_removed_from_route' => 'Driver removed from route successfully',
    'driver_suspended_successfully' => 'Driver suspended successfully',
    'driver_activated_successfully' => 'Driver activated successfully',
    'driver_name_required' => 'Driver name is required',
    'driver_name_max_length' => 'Driver name cannot exceed 255 characters',
    'phone_required' => 'Phone number is required',
    'phone_exists' => 'Phone number already exists',
    'phone_max_length' => 'Phone number cannot exceed 20 characters',
    'driver_email_invalid' => 'Email address is invalid',
    'driver_email_exists' => 'Email address already exists',
    'license_number_required' => 'License number is required',
    'license_number_exists' => 'License number already exists',
    'license_number_max_length' => 'License number cannot exceed 255 characters',
    'license_expiry_required' => 'License expiry date is required',
    'license_expiry_date' => 'License expiry must be a valid date',
    'license_expiry_future' => 'License expiry must be in the future',
    'license_expiry_too_far' => 'License expiry cannot be more than 10 years in the future',
    'national_id_required' => 'National ID is required',
    'national_id_exists' => 'National ID already exists',
    'national_id_max_length' => 'National ID cannot exceed 255 characters',
    'address_max_length' => 'Address cannot exceed 500 characters',
    'hire_date_required' => 'Hire date is required',
    'hire_date_date' => 'Hire date must be a valid date',
    'hire_date_past' => 'Hire date cannot be in the future',
    'hire_date_future' => 'Hire date cannot be in the future',
    'salary_numeric' => 'Salary must be a number',
    'salary_min' => 'Salary must be at least 0',
    'salary_max' => 'Salary cannot exceed 999,999.99',
    'status_required' => 'Status is required',
    'status_invalid' => 'Status must be active, inactive, or suspended',
    'vehicle_type_max_length' => 'Vehicle type cannot exceed 255 characters',
    'vehicle_model_max_length' => 'Vehicle model cannot exceed 255 characters',
    'vehicle_year_max_length' => 'Vehicle year cannot exceed 4 characters',
    'vehicle_plate_max_length' => 'Vehicle plate number cannot exceed 255 characters',
    'vehicle_color_max_length' => 'Vehicle color cannot exceed 255 characters',
    'vehicle_capacity_integer' => 'Vehicle capacity must be a whole number',
    'vehicle_capacity_min' => 'Vehicle capacity must be at least 1',
    'vehicle_capacity_max' => 'Vehicle capacity cannot exceed 100',
    'vehicle_notes_max_length' => 'Vehicle notes cannot exceed 1000 characters',
    'emergency_contact_name_max_length' => 'Emergency contact name cannot exceed 255 characters',
    'emergency_contact_phone_max_length' => 'Emergency contact phone cannot exceed 20 characters',
    'emergency_contact_relation_max_length' => 'Emergency contact relation cannot exceed 255 characters',

    // Security Messages
    'rate_limit_exceeded' => 'Too many requests. Please try again later.',
    'csrf_token_mismatch' => 'Security token mismatch. Please refresh and try again.',
    'invalid_signature' => 'Invalid request signature.',
    'request_timeout' => 'Request timeout. Please try again.',
    'file_too_large' => 'File size exceeds maximum allowed limit.',
    'invalid_file_type' => 'Invalid file type.',
    'upload_failed' => 'File upload failed.',
    'access_denied' => 'Access denied.',
    'session_expired' => 'Session expired. Please login again.',
    'account_suspended' => 'Account suspended. Contact administrator.',
    'ip_blocked' => 'Your IP address has been blocked.',
    'suspicious_activity' => 'Suspicious activity detected.',

    // System Messages
    'system_maintenance' => 'System under maintenance. Please try again later.',
    'database_error' => 'Database error occurred. Please contact support.',
    'server_error' => 'Server error occurred. Please try again later.',
    'service_unavailable' => 'Service temporarily unavailable.',
    'cache_cleared' => 'Cache cleared successfully.',
    'backup_created' => 'Backup created successfully.',
    'backup_restored' => 'Backup restored successfully.',
    'settings_updated' => 'Settings updated successfully.',
    'configuration_saved' => 'Configuration saved successfully.',

    // Custom Validation Messages
    'strong_password' => 'The :attribute must contain at least 8 characters with uppercase, lowercase, number, and special character.',
    'safe_filename' => 'The :attribute contains invalid characters.',
    'no_scripts' => 'The :attribute cannot contain script tags.',
    'no_sql_injection' => 'The :attribute contains potentially dangerous content.',

    // Dashboard specific
    'welcome_back' => 'Welcome back',
    'business_management' => 'Business Management',
    'subscription_management' => 'Subscription Management',
    'active_clients' => 'Active Clients',
    'available_routes' => 'Available Routes',
    'active_drivers' => 'Active Drivers',
    'this_month' => 'This Month',
    'switching_language' => 'Switching Language',
    'loading' => 'Loading',
    'close' => 'Close',
    'logo' => 'Logo',
    'admin' => 'Admin',
    'profile' => 'Profile',
    'database_status' => 'Database Status',
    'cache_status' => 'Cache Status',
    'storage_usage' => 'Storage Usage',
    'memory_usage' => 'Memory Usage',
    'queue_status' => 'Queue Status',
    'system_uptime' => 'System Uptime',
    'active_routes' => 'Active Routes',
    'total' => 'Total',
    'manage_routes' => 'Manage Routes',
    'manage_drivers' => 'Manage Drivers',
    'monthly_revenue' => 'Monthly Revenue',
    'vs_last_month' => 'vs Last Month',
    'on_leave' => 'On Leave',
    'view_all' => 'View All',
    'exporting' => 'Exporting',
    'new_clients' => 'New Clients',
    'revenue_trend' => 'Revenue Trend',
    'client_growth' => 'Client Growth',
    'subscription_status' => 'Subscription Status',
    'driver_status' => 'Driver Status',
    'total_students' => 'Total Students',
    'new_this_month' => 'New This Month',
    'logged_in_today' => 'Logged In Today',
    'view_financial' => 'View Financial',
    'system_health' => 'System Health',

    // Admin Pages - General
    'driver_management' => 'Driver Management',
    'route_management' => 'Route Management',
    'financial_management' => 'Financial Management',
    'subscription_management' => 'Subscription Management',
    'payment_management' => 'Payment Management',
    'add_driver' => 'Add Driver',
    'add_route' => 'Add Route',
    'add_subscription' => 'Add Subscription',
    'driver_list' => 'Driver List',
    'route_list' => 'Route List',

    // Form and Validation
    'please_fix_errors' => 'Please fix the following errors:',
    'subscription_info' => 'Subscription Information',
    'back_to_subscriptions' => 'Back to Subscriptions',
    'validation_errors' => 'Validation Errors',
    'role_information' => 'Role Information',
    'back_to_roles' => 'Back to Roles',
    'no_permissions_description' => 'No permissions available for assignment.',
    'no_permissions_selected_confirm' => 'No permissions selected. Are you sure you want to create this role?',

    // Financial Reports
    'financial_reports' => 'Financial Reports',
    'back_to_dashboard' => 'Back to Dashboard',
    'filters' => 'Filters',
    'total_completed' => 'Total Completed',
    'pending_amount' => 'Pending Amount',
    'collection_rate' => 'Collection Rate',
    'completion_rate' => 'Completion Rate',
    'total_payments' => 'Total Payments',
    'selected_period' => 'Selected Period',
    'subscription_list' => 'Subscription List',

    // Client Management
    'add_new_client' => 'Add New Client',
    'back_to_clients' => 'Back to Clients',

    // Statistics Cards
    'total_drivers' => 'Total Drivers',
    'active_drivers' => 'Active Drivers',
    'suspended_drivers' => 'Suspended Drivers',
    'total_routes' => 'Total Routes',
    'active_routes' => 'Active Routes',
    'total_subscriptions' => 'Total Subscriptions',
    'active_subscriptions' => 'Active Subscriptions',
    'expired_subscriptions' => 'Expired Subscriptions',
    'pending_subscriptions' => 'Pending Subscriptions',
    'monthly_revenue' => 'Monthly Revenue',
    'total_revenue' => 'Total Revenue',
    'pending_payments' => 'Pending Payments',
    'completed_payments' => 'Completed Payments',

    // Actions and Buttons
    'add_new_driver' => 'Add New Driver',
    'add_new_route' => 'Add New Route',
    'add_new_subscription' => 'Add New Subscription',
    'view_details' => 'View Details',
    'edit_driver' => 'Edit Driver',
    'edit_route' => 'Edit Route',
    'edit_subscription' => 'Edit Subscription',
    'suspend_driver' => 'Suspend Driver',
    'activate_driver' => 'Activate Driver',
    'assign_route' => 'Assign Route',
    'unassign_route' => 'Unassign Route',

    // Status Labels
    'driver_status' => 'Driver Status',
    'route_status' => 'Route Status',
    'subscription_status' => 'Subscription Status',
    'payment_status' => 'Payment Status',
    'license_status' => 'License Status',
    'expiring_soon' => 'Expiring Soon',
    'expired' => 'Expired',
    'valid' => 'Valid',
    'unassigned_drivers' => 'Unassigned Drivers',
    'assigned_drivers' => 'Assigned Drivers',

    // Dashboard specific translations
    'expiring_subscriptions_alert' => 'Expiring Subscriptions Alert',
    'expiring_subscriptions_count' => 'Expiring Subscriptions Count',
    'view_subscriptions' => 'View Subscriptions',
    'weekly_revenue' => 'Weekly Revenue',
    'vs_last_week' => 'vs Last Week',
    'system_activity' => 'System Activity',
    'last_7_days' => 'Last 7 Days',
    'failed_payments' => 'Failed Payments',
    'requires_attention' => 'Requires Attention',
    'manage_users' => 'Manage Users',
    'drivers_list' => 'Drivers List',
    'assigned_to_routes' => 'Assigned to Routes',
    'driver_performance_report' => 'Driver Performance Report',
    'performance_report' => 'Performance Report',
    'export_drivers' => 'Export Drivers',
    'subscriptions' => 'Subscriptions',
    'payments' => 'Payments',
    'user_management' => 'User Management',
    'role_management' => 'Role Management',
    'system_management' => 'System Management',
    'switching_language' => 'Switching Language...',
    'language_changed_successfully' => 'Language changed successfully',
    'full_name' => 'Full Name',
    'not_provided' => 'Not provided',
    'contact_information' => 'Contact Information',
    'member_since' => 'Member Since',
    'select_option' => 'Select an option',
    'password' => 'Password',
    'leave_blank_to_keep_current' => 'leave blank to keep current',
    'client_details' => 'Client Details',
    'back_to_clients' => 'Back to Clients',
    'edit_client' => 'Edit Client',
    'basic_information' => 'Basic Information',
    'parent_information' => 'Parent Information',
    'father_job' => 'Father\'s Job',
    'mother_job' => 'Mother\'s Job',
    'father_phone' => 'Father\'s Phone',
    'mother_phone' => 'Mother\'s Phone',
    'home_phone' => 'Home Phone',
    'extra_phone' => 'Extra Phone',
    'contact_priority' => 'Contact Priority',
    'contact_father' => 'Contact Father First',
    'contact_mother' => 'Contact Mother First',
    'area' => 'Area',
    'location' => 'Location',
    'car_type' => 'Car Type',
    'study_start_date' => 'Study Start Date',
    'subscription_type' => 'Subscription Type',
    'subscription_one_time' => 'One Time',
    'subscription_monthly' => 'Monthly',
    'client_type' => 'Client Type',
    'new_client' => 'New Client',
    'old_client' => 'Old Client',
    'additional_notes' => 'Additional Notes',
    'fields_marked_required' => 'Fields marked with * are required',

    // Additional User Management Translations
    'add_user' => 'Add User',
    'create_user' => 'Create User',
    'edit_user' => 'Edit User',
    'show_user' => 'Show User',
    'delete_user' => 'Delete User',
    'user_profile' => 'User Profile',
    'user_information' => 'User Information',
    'user_type' => 'User Type',
    'user_roles' => 'User Roles',
    'user_permissions' => 'User Permissions',
    'profile_photo' => 'Profile Photo',
    'two_factor_authentication' => 'Two Factor Authentication',
    'email_notifications' => 'Email Notifications',
    'sms_notifications' => 'SMS Notifications',
    'timezone' => 'Timezone',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',
    'last_login' => 'Last Login',
    'activity_count' => 'Activity Count',
    'password_requirements' => 'Password Requirements',
    'password_min_8_chars' => 'Minimum 8 characters',
    'password_mixed_case' => 'Mixed case letters',
    'password_numbers' => 'Numbers',
    'password_symbols' => 'Special symbols',
    'user_roles_info' => 'User Roles Information',
    'user_roles_description' => 'Select appropriate roles for this user based on their responsibilities.',
    'please_select_at_least_one_role' => 'Please select at least one role',
    'max_file_size_2mb' => 'Maximum file size: 2MB',
    'verified' => 'Verified',
    'unverified' => 'Unverified',
    'deactivate' => 'Deactivate',
    'activate' => 'Activate',
    'quick_stats' => 'Quick Stats',
    'activities' => 'Activities',
    'never' => 'Never',
    'no_roles_assigned' => 'No roles assigned',
    'no_permissions_assigned' => 'No permissions assigned',
    'recent_activity' => 'Recent Activity',
    'activity' => 'Activity',
    'ip_address' => 'IP Address',
    'no_recent_activity' => 'No recent activity',
    'confirm_user_status_change' => 'Are you sure you want to change the user status?',
    'error_occurred' => 'An error occurred',
    'role_details' => 'Role Details',
    'users_count' => 'Users Count',
    'permissions_count' => 'Permissions Count',
    'role_management_tips' => 'Role Management Tips',
    'role_tip_1' => 'Assign minimal permissions needed for each role',
    'role_tip_2' => 'Regularly review and update role permissions',
    'role_tip_3' => 'Use descriptive names for custom roles',
    'select_all_permissions' => 'Select All Permissions',
    'super_admin_has_all_permissions' => 'Super Admin role has all permissions by default',
    'system_role' => 'System Role',
    'custom_role' => 'Custom Role',
    'guard_name' => 'Guard Name',
    'users_with_role' => 'Users with this Role',
    'view_all_users' => 'View All Users',
    'no_users_with_role' => 'No users have this role',
    'permission_information' => 'Permission Information',
    'permission_name' => 'Permission Name',
    'permission_name_help' => 'Use format: action resource (e.g., create users, read posts)',
    'select_category' => 'Select Category',
    'custom_category' => 'Custom Category',
    'custom_category_name' => 'Custom Category Name',
    'permission_description_help' => 'Optional description of what this permission allows',
    'guard_name_help' => 'Web for browser sessions, API for API access',
    'permission_naming_guide' => 'Permission Naming Guide',
    'naming_conventions' => 'Naming Conventions',
    'create_users_example' => 'Allow creating new users',
    'read_posts_example' => 'Allow viewing posts',
    'update_settings_example' => 'Allow modifying settings',
    'delete_comments_example' => 'Allow deleting comments',
    'available_categories' => 'Available Categories',
    'bulk_create_tip' => 'Bulk Create Tip',
    'bulk_create_description' => 'Create multiple permissions at once for a resource',
    'permission_name_invalid_chars' => 'Permission name can only contain letters, numbers, spaces, hyphens and underscores',
    'permission_name_required' => 'Permission name is required',
    'create_permission' => 'Create Permission',
    'edit_permission' => 'Edit Permission',
    'show_permission' => 'Show Permission',
    'view_permission' => 'View Permission',
    'delete_permission' => 'Delete Permission',
    'permission_details' => 'Permission Details',
    'search_permissions' => 'Search permissions...',
    'all_categories' => 'All Categories',
    'all_guards' => 'All Guards',
    'no_permissions_found' => 'No permissions found',
    'no_permissions_description' => 'No permissions match your search criteria',
    'create_first_permission' => 'Create First Permission',
    'bulk_create_permissions' => 'Bulk Create Permissions',
    'resource_name' => 'Resource Name',
    'resource_name_help' => 'e.g., users, posts, settings',
    'create_permissions' => 'Create Permissions',
    'confirm_delete_permission' => 'Are you sure you want to delete this permission',
    'delete_permission_warning' => 'This action cannot be undone and will remove the permission from all roles and users',
    'edit_permission_warning' => 'Changing this permission may affect system functionality',
    'affected_roles' => 'Affected Roles',
    'no_roles_using_permission' => 'No roles are using this permission',
    'confirm_edit_permission_with_roles' => 'This permission is used by roles. Are you sure you want to edit it?',
    'roles_with_permission' => 'Roles with this Permission',
    'users_with_permission' => 'Users with this Permission',
    'no_roles_with_permission' => 'No roles have this permission',
    'no_users_with_permission' => 'No users have this permission',
    'and_more_users' => 'and :count more users',
    'usage_information' => 'Usage Information',
    'permission_usage' => 'Permission Usage',
    'permission_usage_description' => 'This permission is currently assigned to the following roles and users',
    'related_permissions' => 'Related Permissions',
    'no_related_permissions' => 'No related permissions found',
    'warning' => 'Warning',
    'id' => 'ID',
    'cannot_delete_super_admin' => 'Cannot delete super admin user',
    'cannot_delete_yourself' => 'Cannot delete yourself',
    'user_not_deleted' => 'User is not deleted',
    'user_restored_successfully' => 'User restored successfully',
    'user_permanently_deleted' => 'User permanently deleted',
    'cannot_deactivate_super_admin' => 'Cannot deactivate super admin user',
    'cannot_deactivate_yourself' => 'Cannot deactivate yourself',
    'user_deactivated_successfully' => 'User deactivated successfully',
    'user_activated_successfully' => 'User activated successfully',
    'bulk_actions' => 'Bulk Actions',
    'select_action' => 'Select Action',
    'assign_role' => 'Assign Role',
    'remove_role' => 'Remove Role',
    'select_role' => 'Select Role',
    'apply_action' => 'Apply Action',
    'no_users_selected' => 'No users selected',
    'bulk_action_completed' => 'Bulk action completed successfully',
    'role_created_successfully' => 'Role created successfully',
    'role_updated_successfully' => 'Role updated successfully',
    'role_deleted_successfully' => 'Role deleted successfully',
    'role_cloned_successfully' => 'Role cloned successfully',
    'cannot_delete_super_admin_role' => 'Cannot delete super admin role',
    'role_in_use_cannot_delete' => 'Role is in use and cannot be deleted',
    'confirm_delete_role' => 'Are you sure you want to delete this role',
    'permission_created_successfully' => 'Permission created successfully',
    'permission_updated_successfully' => 'Permission updated successfully',
    'permission_deleted_successfully' => 'Permission deleted successfully',
    'permission_name_exists' => 'Permission name already exists',
    'guard_name_required' => 'Guard name is required',
    'permission_in_use_cannot_delete' => 'Permission is in use and cannot be deleted',
    'bulk_permissions_created' => 'Bulk permissions created',
    'bulk_permissions_created_count' => ':count permissions created successfully',
    'role_permissions_synced' => 'Role permissions synced',
    'role_permissions_synced_successfully' => 'Role permissions synced successfully',
    'user_created_successfully' => 'User created successfully',
    'user_updated_successfully' => 'User updated successfully',
    'user_deleted_successfully' => 'User deleted successfully',
    'user_not_found' => 'User not found',
    'permission_denied' => 'Permission denied',
    'access_denied' => 'Access denied',
    'unauthorized' => 'Unauthorized',
    'forbidden' => 'Forbidden',
    'invalid_credentials' => 'Invalid credentials',
    'login_successful' => 'Login successful',
    'logout_successful' => 'Logout successful',
    'password_reset' => 'Password Reset',
    'forgot_password' => 'Forgot Password',
    'reset_password' => 'Reset Password',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'password_changed_successfully' => 'Password changed successfully',
    'role_name' => 'Role Name',
    'role_description' => 'Role Description',
    'role_permissions' => 'Role Permissions',
    'create_role' => 'Create Role',
    'edit_role' => 'Edit Role',
    'view_role' => 'View Role',
    'delete_role' => 'Delete Role',
    'clone_role' => 'Clone Role',
    'search_roles' => 'Search roles...',
    'no_roles_found' => 'No roles found',
    'create_first_role' => 'Create First Role',
    'view_user' => 'View User',
    'user_details' => 'User Details',
    'bulk_create' => 'Bulk Create',

];
