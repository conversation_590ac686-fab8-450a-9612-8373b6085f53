<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\UsersDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Models\AuditLog;
use App\Models\User;
use App\Models\UserTypes;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    private const INDEX = 'users.index';

    public function __construct()
    {
        //permissions access rights
        $this->middleware(['permission:read users'])->only('index');
        $this->middleware(['permission:create users'])->only('create');
        $this->middleware(['permission:update users'])->only('edit');
        $this->middleware(['permission:delete users'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(UsersDataTable $dataTable)
    {
        return $dataTable->render('content.users.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $user = auth()->user();
        $roles = Role::all();
        $types = UserTypes::all();
        return view('admin.users.create', compact('roles', 'types'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {
        $data = $request->validated();

        // Hash password
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            $data['profile_photo_path'] = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        // Set additional fields
        $data['added_by'] = auth()->id();
        $data['email_verified_at'] = now(); // Auto-verify admin created users

        // Extract roles before creating user
        $roles = $data['roles'] ?? [];
        unset($data['roles']);

        // Create user
        $user = User::create($data);

        // Assign roles
        if (!empty($roles)) {
            $user->syncRoles($roles);
        }

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.user_created_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.user_created_successfully'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = User::find($id);
        if ($user) {
            if ($user->hasRole('super_admin')) {
                abort(401);
            }
            if (auth()->user()->id != $id) {
                $roles = Role::all();
                $types = UserTypes::all();
                return view('admin.users.edit', compact('user', 'roles', 'types'));
            }
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, User $user)
    {
        // Prevent editing super admin by non-super admin
        if ($user->hasRole('super_admin') && !auth()->user()->hasRole('super_admin')) {
            abort(403, __('messages.permission_denied'));
        }

        $data = $request->validated();

        // Handle password update
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->profile_photo_path) {
                Storage::disk('public')->delete($user->profile_photo_path);
            }
            $data['profile_photo_path'] = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        // Extract roles before updating user
        $roles = $data['roles'] ?? [];
        unset($data['roles']);

        // Update user
        $user->update($data);

        // Sync roles
        if (!empty($roles)) {
            $user->syncRoles($roles);
        }

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.user_updated_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.user_updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, User $user)
    {
        // Prevent deleting super admin
        if ($user->hasRole('super_admin')) {
            abort(403, __('messages.cannot_delete_super_admin'));
        }

        // Prevent self-deletion
        if ($user->id === auth()->id()) {
            abort(403, __('messages.cannot_delete_yourself'));
        }

        // Delete profile photo if exists
        if ($user->profile_photo_path) {
            Storage::disk('public')->delete($user->profile_photo_path);
        }

        // Soft delete the user
        $user->delete();

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.user_deleted_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.user_deleted_successfully'));
    }

    /**
     * Show user profile
     */
    public function show(User $user)
    {
        $user->load(['type', 'roles', 'audit_logs' => function($query) {
            $query->latest()->limit(10);
        }]);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Restore soft deleted user
     */
    public function restore(Request $request, $id)
    {
        $user = User::withTrashed()->findOrFail($id);

        if (!$user->trashed()) {
            return redirect()->back()
                ->with('error', __('messages.user_not_deleted'));
        }

        $user->restore();

        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.user_restored_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return redirect()->back()
            ->with('status', __('messages.user_restored_successfully'));
    }

    /**
     * Permanently delete user
     */
    public function forceDelete(Request $request, $id)
    {
        $user = User::withTrashed()->findOrFail($id);

        // Prevent force deleting super admin
        if ($user->hasRole('super_admin')) {
            abort(403, __('messages.cannot_delete_super_admin'));
        }

        // Delete profile photo if exists
        if ($user->profile_photo_path) {
            Storage::disk('public')->delete($user->profile_photo_path);
        }

        $user->forceDelete();

        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.user_permanently_deleted'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.user_permanently_deleted'));
    }

    /**
     * Toggle user active status
     */
    public function toggleStatus(Request $request, User $user)
    {
        // Prevent deactivating super admin
        if ($user->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => __('messages.cannot_deactivate_super_admin')
            ]);
        }

        // Prevent self-deactivation
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => __('messages.cannot_deactivate_yourself')
            ]);
        }

        $isActive = $user->deleted_at === null;

        if ($isActive) {
            $user->delete(); // Soft delete to deactivate
            $message = __('messages.user_deactivated_successfully');
            $description = 'User deactivated';
        } else {
            $user->restore(); // Restore to activate
            $message = __('messages.user_activated_successfully');
            $description = 'User activated';
        }

        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => $description,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($user),
            'relationmodel_id' => $user->id
        ]);

        return response()->json([
            'success' => true,
            'message' => $message,
            'status' => !$isActive
        ]);
    }

    /**
     * create user by Ajax request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function create_ajax(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:80',
            'phone' => ['required', 'numeric', 'regex:/^[0-9]{11}$/', 'unique:users,phone,NULL,id,deleted_at,NULL'],
            'address' => 'required|string',
            'shipping_country_id' => 'required|integer',
        ]);
        $data['email'] = $data['phone'] . '@tb3nalk.com';
        $data['password'] = bcrypt('TB@123456');
        $data['added_by'] = auth()->user()->id;
        $data['user_type_id'] = 2;
        $item = User::create($data);
        return response()->json(array('success' => true, 'data' => ['item' => $item]));
    }

    /**
     * return users list for Ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list()
    {
        $list = User::where('user_type_id', 2)->get();
        return response()->json(array('success' => true, 'data' => ['list' => $list]));
    }

    /**
     * Handle bulk actions on users
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,assign_role,remove_role,delete',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id',
            'role_id' => 'required_if:action,assign_role,remove_role|exists:roles,id',
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        $action = $request->action;
        $affectedCount = 0;

        DB::transaction(function () use ($users, $action, $request, &$affectedCount) {
            foreach ($users as $user) {
                // Skip super admin users for safety
                if ($user->hasRole('super_admin') && !auth()->user()->hasRole('super_admin')) {
                    continue;
                }

                switch ($action) {
                    case 'activate':
                        if ($user->trashed()) {
                            $user->restore();
                            $affectedCount++;
                        }
                        break;

                    case 'deactivate':
                        if (!$user->trashed()) {
                            $user->delete();
                            $affectedCount++;
                        }
                        break;

                    case 'assign_role':
                        $role = Role::find($request->role_id);
                        if ($role && !$user->hasRole($role)) {
                            $user->assignRole($role);
                            $affectedCount++;
                        }
                        break;

                    case 'remove_role':
                        $role = Role::find($request->role_id);
                        if ($role && $user->hasRole($role)) {
                            $user->removeRole($role);
                            $affectedCount++;
                        }
                        break;

                    case 'delete':
                        if (!$user->hasRole('super_admin')) {
                            $user->forceDelete();
                            $affectedCount++;
                        }
                        break;
                }
            }
        });

        // Log bulk action
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => "Bulk action '{$action}' performed on {$affectedCount} users",
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => User::class,
            'relationmodel_id' => null
        ]);

        $message = __('messages.bulk_action_completed', ['count' => $affectedCount, 'action' => $action]);
        return redirect()->route('admin.users.index')->with('success', $message);
    }

    /**
     * Search auto complete.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function autoComplete(Request $request)
    {
        $term = $request->term;
        $results = [];
        $results[] = ['id' => '', 'title' => $term, 'value' => __('add_client')];
        $queries = User::where('user_type_id', 2)->where(
            function ($query) use ($term) {
                $query->orWhere('name', 'like', '%' . $term . '%');
                $query->orWhere('phone', 'like', '%' . $term . '%');
            }
        )->get();
        foreach ($queries as $query):
            $results[] = ['id' => $query->id, 'value' => $query->name, 'phone' => $query->phone, 'address' => $query->address, 'shipping_country_id' => $query->shipping_country_id];
        endforeach;
        return response()->json($results);
    }

}
