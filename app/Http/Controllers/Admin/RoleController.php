<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\RoleRequest;
use App\Models\AuditLog;
use App\Models\Level;
use App\Models\User;
use App\DataTables\RolesDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller {

    private const INDEX = 'admin.roles.index';

    public function __construct() {
        $this->middleware(['permission:read roles'])->only('index', 'show');
        $this->middleware(['permission:create roles'])->only('create', 'store');
        $this->middleware(['permission:update roles'])->only('edit', 'update');
        $this->middleware(['permission:delete roles'])->only('destroy');
    }

    /**
     * Display a listing of roles with their permissions
     */
    public function index(RolesDataTable $dataTable) {
        return $dataTable->render('admin.roles.index');
    }

    /**
     * Show the form for creating a new role
     */
    public function create() {
        $permissions = Permission::all()->groupBy(function($permission) {
            return $this->extractCategoryFromPermission($permission->name);
        });

        $permissionCategories = $this->getPermissionCategories();

        return view('admin.roles.create', compact('permissions', 'permissionCategories'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request) {
        $request->validate([
            'name' => 'required|string|unique:roles,name|max:100',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ], [
            'name.required' => __('messages.role_name_required'),
            'name.unique' => __('messages.role_name_exists'),
            'name.max' => __('messages.role_name_max_length')
        ]);

        $role = Role::create([
            'name' => $request->name,
            'guard_name' => 'web',
            'description' => $request->description
        ]);

        // Assign permissions
        if ($request->permissions) {
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->syncPermissions($permissions);
        }

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.role_created_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($role),
            'relationmodel_id' => $role->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.role_created_successfully'));
    }

    /**
     * Display the specified role
     */
    public function show(Role $role) {
        $role->load(['permissions', 'users']);
        $usersCount = $role->users()->count();
        $permissionsCount = $role->permissions()->count();

        return view('admin.roles.show', compact('role', 'usersCount', 'permissionsCount'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role) {
        // Prevent editing super admin role by non-super admin
        if ($role->name === 'super_admin' && !auth()->user()->hasRole('super_admin')) {
            abort(403, __('messages.permission_denied'));
        }

        $permissions = Permission::all()->groupBy(function($permission) {
            return $this->extractCategoryFromPermission($permission->name);
        });

        $permissionCategories = $this->getPermissionCategories();
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.roles.edit', compact('role', 'permissions', 'permissionCategories', 'rolePermissions'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role) {
        // Prevent editing super admin role by non-super admin
        if ($role->name === 'super_admin' && !auth()->user()->hasRole('super_admin')) {
            abort(403, __('messages.permission_denied'));
        }

        $request->validate([
            'name' => 'required|string|max:100|unique:roles,name,' . $role->id,
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ], [
            'name.required' => __('messages.role_name_required'),
            'name.unique' => __('messages.role_name_exists'),
            'name.max' => __('messages.role_name_max_length')
        ]);

        $role->update([
            'name' => $request->name,
            'description' => $request->description
        ]);

        // Sync permissions
        if ($request->permissions) {
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->syncPermissions($permissions);
        } else {
            $role->syncPermissions([]);
        }

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.role_updated_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($role),
            'relationmodel_id' => $role->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.role_updated_successfully'));
    }

    /**
     * Remove the specified role
     */
    public function destroy(Request $request, Role $role) {
        // Prevent deleting super admin role
        if ($role->name === 'super_admin') {
            abort(403, __('messages.cannot_delete_super_admin_role'));
        }

        // Check if role is assigned to any users
        if ($role->users()->count() > 0) {
            return redirect()->back()
                ->with('error', __('messages.role_in_use_cannot_delete'));
        }

        $roleName = $role->name;
        $role->delete();

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.role_deleted_successfully') . ': ' . $roleName,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => Role::class,
            'relationmodel_id' => null
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.role_deleted_successfully'));
    }

    /**
     * Clone a role with its permissions
     */
    public function clone(Request $request, Role $role) {
        $request->validate([
            'name' => 'required|string|unique:roles,name|max:100'
        ]);

        $newRole = Role::create([
            'name' => $request->name,
            'guard_name' => 'web',
            'description' => $role->description . ' (Copy)'
        ]);

        // Copy permissions
        $newRole->syncPermissions($role->permissions);

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.role_cloned_successfully') . ': ' . $role->name . ' -> ' . $newRole->name,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($newRole),
            'relationmodel_id' => $newRole->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.role_cloned_successfully'));
    }

    /**
     * Get permission categories
     */
    private function getPermissionCategories()
    {
        return [
            'admin_clients' => __('messages.client_management'),
            'admin_routes' => __('messages.route_management'),
            'admin_drivers' => __('messages.driver_management'),
            'admin_financial' => __('messages.financial_management'),
            'admin_subscriptions' => __('messages.subscription_management'),
            'admin_payments' => __('messages.payment_management'),
            'users' => __('messages.user_management'),
            'roles' => __('messages.role_management'),
            'permissions' => __('messages.permission_management'),
            'translations' => __('messages.translation_management'),
            'system' => __('messages.system_management')
        ];
    }

    /**
     * Extract category from permission name
     */
    private function extractCategoryFromPermission($permissionName)
    {
        $parts = explode(' ', $permissionName);
        if (count($parts) >= 2) {
            return $parts[1]; // e.g., "create admin_clients" -> "admin_clients"
        }

        return 'other';
    }

}
