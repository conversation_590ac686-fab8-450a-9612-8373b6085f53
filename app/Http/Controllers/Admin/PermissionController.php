<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\DataTables\PermissionsDataTable;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionController extends Controller
{
    private const INDEX = 'admin.permissions.index';

    public function __construct()
    {
        $this->middleware(['permission:read permissions'])->only('index', 'show');
        $this->middleware(['permission:create permissions'])->only('create', 'store');
        $this->middleware(['permission:update permissions'])->only('edit', 'update');
        $this->middleware(['permission:delete permissions'])->only('destroy');
    }

    /**
     * Display a listing of permissions
     */
    public function index(PermissionsDataTable $dataTable)
    {
        $categories = $this->getPermissionCategories();
        return $dataTable->render('admin.permissions.index', compact('categories'));
    }

    /**
     * Show the form for creating a new permission
     */
    public function create()
    {
        $categories = $this->getPermissionCategories();
        $actions = ['create', 'read', 'update', 'delete', 'view', 'manage'];
        
        return view('admin.permissions.create', compact('categories', 'actions'));
    }

    /**
     * Store a newly created permission
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:permissions,name|max:255',
            'guard_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:100'
        ], [
            'name.required' => __('messages.permission_name_required'),
            'name.unique' => __('messages.permission_name_exists'),
            'guard_name.required' => __('messages.guard_name_required')
        ]);

        $permission = Permission::create([
            'name' => $request->name,
            'guard_name' => $request->guard_name ?? 'web',
            'description' => $request->description,
            'category' => $request->category
        ]);

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.permission_created_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($permission),
            'relationmodel_id' => $permission->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.permission_created_successfully'));
    }

    /**
     * Display the specified permission
     */
    public function show(Permission $permission)
    {
        $permission->load('roles');
        $usersWithPermission = $permission->users()->with('roles')->get();
        
        return view('admin.permissions.show', compact('permission', 'usersWithPermission'));
    }

    /**
     * Show the form for editing the specified permission
     */
    public function edit(Permission $permission)
    {
        $categories = $this->getPermissionCategories();
        $actions = ['create', 'read', 'update', 'delete', 'view', 'manage'];
        
        return view('admin.permissions.edit', compact('permission', 'categories', 'actions'));
    }

    /**
     * Update the specified permission
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'guard_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:100'
        ], [
            'name.required' => __('messages.permission_name_required'),
            'name.unique' => __('messages.permission_name_exists'),
            'guard_name.required' => __('messages.guard_name_required')
        ]);

        $permission->update([
            'name' => $request->name,
            'guard_name' => $request->guard_name,
            'description' => $request->description,
            'category' => $request->category
        ]);

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.permission_updated_successfully'),
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($permission),
            'relationmodel_id' => $permission->id
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.permission_updated_successfully'));
    }

    /**
     * Remove the specified permission
     */
    public function destroy(Request $request, Permission $permission)
    {
        // Check if permission is assigned to any roles or users
        if ($permission->roles()->count() > 0 || $permission->users()->count() > 0) {
            return redirect()->back()
                ->with('error', __('messages.permission_in_use_cannot_delete'));
        }

        $permissionName = $permission->name;
        $permission->delete();

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.permission_deleted_successfully') . ': ' . $permissionName,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => Permission::class,
            'relationmodel_id' => null
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.permission_deleted_successfully'));
    }

    /**
     * Bulk create permissions for a resource
     */
    public function bulkCreate(Request $request)
    {
        $request->validate([
            'resource_name' => 'required|string|max:100',
            'actions' => 'required|array|min:1',
            'actions.*' => 'string|in:create,read,update,delete,view,manage',
            'guard_name' => 'required|string|max:255',
            'category' => 'nullable|string|max:100'
        ]);

        $createdPermissions = [];
        $resourceName = $request->resource_name;
        $guardName = $request->guard_name ?? 'web';
        $category = $request->category;

        foreach ($request->actions as $action) {
            $permissionName = $action . ' ' . $resourceName;
            
            // Check if permission already exists
            if (!Permission::where('name', $permissionName)->exists()) {
                $permission = Permission::create([
                    'name' => $permissionName,
                    'guard_name' => $guardName,
                    'description' => ucfirst($action) . ' ' . ucfirst($resourceName),
                    'category' => $category
                ]);
                
                $createdPermissions[] = $permission;
            }
        }

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.bulk_permissions_created') . ': ' . count($createdPermissions) . ' permissions for ' . $resourceName,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => Permission::class
        ]);

        return redirect()->route(self::INDEX)
            ->with('status', __('messages.bulk_permissions_created_count', ['count' => count($createdPermissions)]));
    }

    /**
     * Sync permissions with roles
     */
    public function syncWithRoles(Request $request)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ]);

        $role = Role::findOrFail($request->role_id);
        $permissions = Permission::whereIn('id', $request->permissions ?? [])->get();
        
        $role->syncPermissions($permissions);

        // Log activity
        AuditLog::create([
            'user_id' => auth()->id(),
            'description' => __('messages.role_permissions_synced') . ': ' . $role->name,
            'ip_address' => $request->getClientIp(),
            'relationmodel_type' => get_class($role),
            'relationmodel_id' => $role->id
        ]);

        return response()->json([
            'success' => true,
            'message' => __('messages.role_permissions_synced_successfully')
        ]);
    }

    /**
     * Get permission categories
     */
    private function getPermissionCategories()
    {
        return [
            'admin_clients' => __('messages.client_management'),
            'admin_routes' => __('messages.route_management'),
            'admin_drivers' => __('messages.driver_management'),
            'admin_financial' => __('messages.financial_management'),
            'admin_subscriptions' => __('messages.subscription_management'),
            'admin_payments' => __('messages.payment_management'),
            'users' => __('messages.user_management'),
            'roles' => __('messages.role_management'),
            'permissions' => __('messages.permission_management'),
            'translations' => __('messages.translation_management'),
            'system' => __('messages.system_management')
        ];
    }

    /**
     * Group permissions by category
     */
    private function groupPermissionsByCategory($permissions)
    {
        $grouped = [];
        
        foreach ($permissions as $permission) {
            $category = $this->extractCategoryFromPermission($permission->name);
            $grouped[$category][] = $permission;
        }
        
        return $grouped;
    }

    /**
     * Extract category from permission name
     */
    private function extractCategoryFromPermission($permissionName)
    {
        $parts = explode(' ', $permissionName);
        if (count($parts) >= 2) {
            return $parts[1]; // e.g., "create admin_clients" -> "admin_clients"
        }
        
        return 'other';
    }
}
