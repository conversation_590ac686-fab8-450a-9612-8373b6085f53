<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', __('messages.permission_denied'));
        }

        // Check if user has any admin permissions
        $user = Auth::user();
        
        // List of admin permissions to check
        $adminPermissions = [
            'read admin_clients',
            'create admin_clients',
            'update admin_clients',
            'delete admin_clients',
            'read admin_routes',
            'create admin_routes',
            'update admin_routes',
            'delete admin_routes',
            'read admin_drivers',
            'create admin_drivers',
            'update admin_drivers',
            'delete admin_drivers',
            'read admin_financial',
            'update admin_financial',
            'read admin_subscriptions',
            'read admin_payments',
            'read users',
            'create users',
            'update users',
            'delete users',
            'read roles',
            'create roles',
            'update roles',
            'delete roles',
            'read permissions',
            'create permissions',
            'update permissions',
            'delete permissions',
        ];

        // Check if user has at least one admin permission
        $hasAdminAccess = false;
        foreach ($adminPermissions as $permission) {
            if ($user->can($permission)) {
                $hasAdminAccess = true;
                break;
            }
        }

        if (!$hasAdminAccess) {
            return redirect()->route('dashboard')->with('error', __('messages.permission_denied'));
        }

        return $next($request);
    }
}
