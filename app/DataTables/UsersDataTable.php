<?php

namespace App\DataTables;

use App\Models\User;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UsersDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', function ($row) {
                return view('admin.users.actions', [
                    'id' => $row->id,
                    'name' => $row->name,
                    'is_active' => $row->is_active
                ])->render();
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->addColumn('status', function ($row) {
                if ($row->is_active) {
                    return '<span class="badge bg-success">' . __('messages.active') . '</span>';
                } else {
                    return '<span class="badge bg-danger">' . __('messages.inactive') . '</span>';
                }
            })
            ->editColumn('roles', function ($row) {
                $roles = $row->roles->pluck('name')->toArray();
                if (empty($roles)) {
                    return '<span class="text-muted">' . __('messages.no_roles_assigned') . '</span>';
                }
                return implode(' ', array_map(function($role) {
                    return '<span class="badge bg-primary me-1">' . $role . '</span>';
                }, $roles));
            })
            ->addColumn('user_type', function ($row) {
                return $row->type ? $row->type->name : __('messages.not_provided');
            })
            ->editColumn('email_verified_at', function ($row) {
                if ($row->email_verified_at) {
                    return '<span class="badge bg-success">' . __('messages.verified') . '</span>';
                } else {
                    return '<span class="badge bg-warning">' . __('messages.unverified') . '</span>';
                }
            })
            ->rawColumns(['actions', 'status', 'roles', 'email_verified_at']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\User $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(User $model)
    {
        $user = auth()->user();
        $fetch = $model->with(['roles', 'type', 'main_account']);
        if (in_array($user->user_type_id, [3, 4])) {
            $fetch->where('user_type_id', $user->user_type_id);
            if ($user->main_account_id) {
                $fetch->where('added_by', $user->id);
            } else {
                $fetch->where('main_account_id', $user->id);
            }
            $fetch->where('id', '!=', $user->id);
        }
        return $fetch;
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    // end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('users-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $cols = [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('name')->addClass('text-center')->title(__('messages.name')),
            Column::make('email')->addClass('text-center')->title(__('messages.email')),
            Column::make('phone')->addClass('text-center')->title(__('messages.phone')),
            Column::computed('user_type')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.user_type')),
            Column::computed('status')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.status')),
            Column::make('roles')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.user_roles')),
            Column::computed('email_verified_at')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.verified')),
            Column::make('created_at')->addClass('text-center')->title(__('messages.created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')
                ->title(__('messages.actions')),
        ];
        return $cols;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Users_' . date('YmdHis');
    }

}
