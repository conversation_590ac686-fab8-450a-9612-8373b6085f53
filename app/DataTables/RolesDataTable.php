<?php

namespace App\DataTables;

use Spatie\Permission\Models\Role;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Illuminate\Support\Facades\DB;

class RolesDataTable extends DataTable {

    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query) {
        return datatables()
                        ->eloquent($query)
                        ->setRowClass(function ($row) {
                            return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
                        })
                        ->addColumn('actions', 'admin.roles.actions')
                        ->editColumn('created_at', function ($row) {
                            return date('Y-m-d h:i A', strtotime($row->created_at));
                        })
                        ->addColumn('users_count', function ($row) {
                            return '<span class="badge bg-primary">' . $row->users->count() . '</span>';
                        })
                        ->addColumn('permissions_count', function ($row) {
                            return '<span class="badge bg-success">' . $row->permissions->count() . '</span>';
                        })
                        ->editColumn('name', function ($row) {
                            if ($row->name === 'super_admin') {
                                return $row->name . ' <span class="badge bg-danger ms-1">' . __('messages.system_role') . '</span>';
                            }
                            return $row->name;
                        })
                        ->rawColumns(['actions', 'users_count', 'permissions_count', 'name']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Role $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Role $model) {
        return $model->with(['users', 'permissions'])->newQuery()->select('roles.*');
    }

    public function getLang() {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } else if (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

// end of getLang method

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html() {
        return $this->builder()
                        ->setTableId('roles-table')
                        ->columns($this->getColumns())
                        ->minifiedAjax()
                        ->dom('Bfrtip')
                        ->orderBy(0, 'desc')
                        ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
                        ->parameters([
                            'dom' => 'Blfrtip',
                        ])
                        ->buttons(
                                Button::make('excel')->text(__('Excel')),
                                Button::make('print')->text(__('Print')),
                        )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns() {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('name')->addClass('text-center')->title(__('messages.role_name')),
            Column::make('description')->addClass('text-center')->title(__('messages.role_description')),
            Column::computed('users_count')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.users_count')),
            Column::computed('permissions_count')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.permissions_count')),
            Column::make('created_at')->addClass('text-center')->title(__('messages.created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')
                ->title(__('messages.actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string {
        return 'Roles_' . date('YmdHis');
    }

}
