<?php

namespace App\DataTables;

use Spatie\Permission\Models\Permission;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class PermissionsDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-white' : '';
            })
            ->addColumn('actions', 'admin.permissions.actions')
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->addColumn('category', function ($row) {
                $category = explode(' ', $row->name)[1] ?? 'general';
                return '<span class="badge bg-info">' . ucfirst($category) . '</span>';
            })
            ->addColumn('roles_count', function ($row) {
                return '<span class="badge bg-primary">' . $row->roles->count() . '</span>';
            })
            ->addColumn('users_count', function ($row) {
                return '<span class="badge bg-success">' . $row->users->count() . '</span>';
            })
            ->editColumn('guard_name', function ($row) {
                return '<span class="badge bg-secondary">' . $row->guard_name . '</span>';
            })
            ->rawColumns(['actions', 'category', 'roles_count', 'users_count', 'guard_name']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \Spatie\Permission\Models\Permission $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(Permission $model)
    {
        return $model->with(['roles', 'users'])->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('permissions-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(0, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('messages.all')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(false)
            ->buttons(
                Button::make('excel')->text(__('messages.export')),
                Button::make('print')->text(__('messages.print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('name')->addClass('text-center')->title(__('messages.permission_name')),
            Column::computed('category')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.category')),
            Column::computed('guard_name')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.guard_name')),
            Column::computed('roles_count')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.roles_count')),
            Column::computed('users_count')
                ->sortable(false)
                ->searchable(false)
                ->addClass('text-center')
                ->title(__('messages.users_count')),
            Column::make('created_at')->addClass('text-center')->title(__('messages.created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')
                ->title(__('messages.actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'Permissions_' . date('YmdHis');
    }
}
