{"dashboard": "Dashboard", "users": "Users", "roles": "Roles", "permissions": "Permissions", "clients": "Clients", "routes": "Routes", "drivers": "Drivers", "subscriptions": "Subscriptions", "payments": "Payments", "students": "Students", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "name": "Name", "phone": "Phone", "address": "Address", "status": "Status", "actions": "Actions", "create": "Create", "edit": "Edit", "update": "Update", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "submit": "Submit", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "first": "First", "last": "Last", "showing": "Showing", "of": "of", "entries": "entries", "no_data": "No data available", "loading": "Loading...", "processing": "Processing...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "confirmation": "Confirmation", "are_you_sure": "Are you sure?", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "created_at": "Created At", "updated_at": "Updated At", "created_by": "Created By", "updated_by": "Updated By", "date": "Date", "time": "Time", "datetime": "Date & Time", "total": "Total", "subtotal": "Subtotal", "amount": "Amount", "price": "Price", "quantity": "Quantity", "description": "Description", "notes": "Notes", "comments": "Comments", "type": "Type", "category": "Category", "title": "Title", "content": "Content", "image": "Image", "file": "File", "upload": "Upload", "download": "Download", "required": "Required", "optional": "Optional", "select": "Select", "choose": "<PERSON><PERSON>", "browse": "Browse", "clear": "Clear", "reset": "Reset", "apply": "Apply", "confirm": "Confirm", "approve": "Approve", "reject": "Reject", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted", "restored": "Restored", "language": "Language", "english": "English", "arabic": "Arabic", "change_language": "Change Language", "welcome": "Welcome", "hello": "Hello", "goodbye": "Goodbye", "thank_you": "Thank You", "please": "Please", "sorry": "Sorry", "excuse_me": "Excuse Me", "help": "Help", "support": "Support", "contact": "Contact", "about": "About", "privacy": "Privacy", "terms": "Terms", "copyright": "Copyright", "all_rights_reserved": "All Rights Reserved", "user_management": "User Management", "role_management": "Role Management", "permission_management": "Permission Management", "add_user": "Add User", "create_user": "Create User", "edit_user": "Edit User", "show_user": "Show User", "delete_user": "Delete User", "user_profile": "User Profile", "user_information": "User Information", "user_type": "User Type", "user_roles": "User Roles", "user_permissions": "User Permissions", "profile_photo": "Profile Photo", "two_factor_authentication": "Two Factor Authentication", "email_notifications": "Email Notifications", "sms_notifications": "SMS Notifications", "timezone": "Timezone", "date_format": "Date Format", "time_format": "Time Format", "last_login": "Last Login", "member_since": "Member Since", "activity_count": "Activity Count", "validation_errors": "Please fix the following errors:", "password_requirements": "Password Requirements", "password_min_8_chars": "Minimum 8 characters", "password_mixed_case": "Mixed case letters", "password_numbers": "Numbers", "password_symbols": "Special symbols", "user_roles_info": "User Roles Information", "user_roles_description": "Select appropriate roles for this user based on their responsibilities.", "please_select_at_least_one_role": "Please select at least one role", "max_file_size_2mb": "Maximum file size: 2MB", "leave_blank_to_keep_current": "Leave blank to keep current password", "verified": "Verified", "unverified": "Unverified", "deactivate": "Deactivate", "activate": "Activate", "quick_stats": "Quick Stats", "activities": "Activities", "basic_information": "Basic Information", "not_provided": "Not provided", "never": "Never", "no_roles_assigned": "No roles assigned", "no_permissions_assigned": "No permissions assigned", "recent_activity": "Recent Activity", "activity": "Activity", "ip_address": "IP Address", "no_recent_activity": "No recent activity", "confirm_user_status_change": "Are you sure you want to change the user status?", "error_occurred": "An error occurred", "role_information": "Role Information", "role_details": "Role Details", "users_count": "Users Count", "permissions_count": "Permissions Count", "role_management_tips": "Role Management Tips", "role_tip_1": "Assign minimal permissions needed for each role", "role_tip_2": "Regularly review and update role permissions", "role_tip_3": "Use descriptive names for custom roles", "select_all_permissions": "Select All Permissions", "super_admin_has_all_permissions": "Super Admin role has all permissions by default", "system_role": "System Role", "custom_role": "Custom Role", "guard_name": "Guard Name", "users_with_role": "Users with this Role", "view_all_users": "View All Users", "no_users_with_role": "No users have this role", "permission_information": "Permission Information", "permission_name": "Permission Name", "permission_name_help": "Use format: action resource (e.g., create users, read posts)", "select_category": "Select Category", "custom_category": "Custom Category", "custom_category_name": "Custom Category Name", "permission_description_help": "Optional description of what this permission allows", "guard_name_help": "Web for browser sessions, API for API access", "permission_naming_guide": "Permission Naming Guide", "naming_conventions": "Naming Conventions", "create_users_example": "Allow creating new users", "read_posts_example": "Allow viewing posts", "update_settings_example": "Allow modifying settings", "delete_comments_example": "Allow deleting comments", "available_categories": "Available Categories", "bulk_create_tip": "Bulk Create Tip", "bulk_create_description": "Create multiple permissions at once for a resource", "permission_name_invalid_chars": "Permission name can only contain letters, numbers, spaces, hyphens and underscores", "permission_name_required": "Permission name is required", "create_permission": "Create Permission", "edit_permission": "Edit Permission", "show_permission": "Show Permission", "view_permission": "View Permission", "delete_permission": "Delete Permission", "permission_details": "Permission Details", "search_permissions": "Search permissions...", "all_categories": "All Categories", "all_guards": "All Guards", "no_permissions_found": "No permissions found", "no_permissions_description": "There are no permissions in the system. Please create permissions first.", "create_first_permission": "Create First Permission", "bulk_create_permissions": "Bulk Create Permissions", "resource_name": "Resource Name", "resource_name_help": "e.g., users, posts, settings", "create_permissions": "Create Permissions", "confirm_delete_permission": "Are you sure you want to delete this permission", "delete_permission_warning": "This action cannot be undone and will remove the permission from all roles and users", "edit_permission_warning": "Changing this permission may affect system functionality", "affected_roles": "Affected Roles", "no_roles_using_permission": "No roles are using this permission", "confirm_edit_permission_with_roles": "This permission is used by roles. Are you sure you want to edit it?", "roles_with_permission": "Roles with this Permission", "users_with_permission": "Users with this Permission", "no_roles_with_permission": "No roles have this permission", "no_users_with_permission": "No users have this permission", "and_more_users": "and :count more users", "usage_information": "Usage Information", "permission_usage": "Permission Usage", "permission_usage_description": "This permission is currently assigned to the following roles and users", "related_permissions": "Related Permissions", "no_related_permissions": "No related permissions found", "id": "ID", "user_created_successfully": "User created successfully", "user_updated_successfully": "User updated successfully", "user_deleted_successfully": "User deleted successfully", "user_not_found": "User not found", "permission_denied": "Permission denied", "access_denied": "Access denied", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "invalid_credentials": "Invalid credentials", "login_successful": "Login successful", "logout_successful": "Logout successful", "password_reset": "Password Reset", "forgot_password": "Forgot Password", "reset_password": "Reset Password", "change_password": "Change Password", "current_password": "Current Password", "new_password": "New Password", "password_changed_successfully": "Password changed successfully", "transport_management": "Transport Management", "client_management": "Client Management", "route_management": "Route Management", "driver_management": "Driver Management", "subscription_management": "Subscription Management", "payment_management": "Payment Management", "student_management": "Student Management", "create_client": "Create Client", "edit_client": "Edit Client", "delete_client": "Delete Client", "client_created_successfully": "Client created successfully", "client_updated_successfully": "Client updated successfully", "client_deleted_successfully": "Client deleted successfully", "client_not_found": "Client not found", "create_route": "Create Route", "edit_route": "Edit Route", "delete_route": "Delete Route", "route_created_successfully": "Route created successfully", "route_updated_successfully": "Route updated successfully", "route_deleted_successfully": "Route deleted successfully", "route_not_found": "Route not found", "route_name": "Route Name", "start_location": "Start Location", "end_location": "End Location", "distance": "Distance", "duration": "Duration", "fare": "Fare", "create_driver": "Create Driver", "edit_driver": "Edit Driver", "delete_driver": "Delete Driver", "driver_created_successfully": "Driver created successfully", "driver_updated_successfully": "Driver updated successfully", "driver_deleted_successfully": "Driver deleted successfully", "driver_not_found": "Driver not found", "driver_name": "Driver Name", "license_number": "License Number", "vehicle_info": "Vehicle Information", "vehicle_number": "Vehicle Number", "vehicle_type": "Vehicle Type", "vehicle_capacity": "Vehicle Capacity", "create_subscription": "Create Subscription", "edit_subscription": "Edit Subscription", "delete_subscription": "Delete Subscription", "subscription_created_successfully": "Subscription created successfully", "subscription_updated_successfully": "Subscription updated successfully", "subscription_deleted_successfully": "Subscription deleted successfully", "subscription_not_found": "Subscription not found", "subscription_type": "Subscription Type", "subscription_period": "Subscription Period", "start_date": "Start Date", "end_date": "End Date", "monthly": "Monthly", "yearly": "Yearly", "weekly": "Weekly", "daily": "Daily", "create_payment": "Create Payment", "edit_payment": "Edit Payment", "delete_payment": "Delete Payment", "payment_created_successfully": "Payment created successfully", "payment_updated_successfully": "Payment updated successfully", "payment_deleted_successfully": "Payment deleted successfully", "payment_not_found": "Payment not found", "payment_method": "Payment Method", "payment_status": "Payment Status", "payment_date": "Payment Date", "payment_amount": "Payment Amount", "cash": "Cash", "credit_card": "Credit Card", "bank_transfer": "Bank Transfer", "online_payment": "Online Payment", "paid": "Paid", "unpaid": "Unpaid", "partial": "Partial", "refunded": "Refunded", "failed": "Failed", "due_date": "Due Date", "payment_reference": "Payment Reference", "payment_details": "Payment Details", "transaction_id": "Transaction ID", "bank_name": "Bank Name", "account_number": "Account Number", "reference_number": "Reference Number", "overdue_payments": "Overdue Payments", "generate_invoice": "Generate Invoice", "bulk_update": "Bulk Update", "export_payments": "Export Payments", "cannot_delete_completed_payment": "Cannot delete completed payment", "cannot_change_completed_to_pending": "Cannot change completed payment to pending", "payment_status_updated_successfully": "Payment status updated successfully", "payments_bulk_updated": ":count payments updated successfully", "subscription_required": "Subscription is required", "subscription_not_active": "Subscription is not active", "amount_required": "Amount is required", "amount_numeric": "Amount must be a number", "amount_min": "Amount must be at least 0", "amount_max": "Amount cannot exceed 999,999.99", "amount_exceeds_subscription": "Amount cannot exceed subscription price", "payment_method_required": "Payment method is required", "payment_method_invalid": "Invalid payment method", "status_required": "Status is required", "status_invalid": "Status must be active, inactive, or suspended", "payment_date_required": "Payment date is required", "payment_date_invalid": "Invalid payment date", "due_date_required": "Due date is required", "due_date_invalid": "Invalid due date", "due_date_after_payment": "Due date must be on or after payment date", "notes_max_length": "Notes cannot exceed 1000 characters", "client_required": "Client is required", "route_required": "Route is required", "student_name_required": "Student name is required", "student_name_max_length": "Student name cannot exceed 255 characters", "subscription_type_required": "Subscription type is required", "subscription_type_invalid": "Invalid subscription type", "price_required": "Price is required", "price_numeric": "Price must be a number", "price_min": "Price must be at least 0", "price_max": "Price cannot exceed 999,999.99", "start_date_required": "Start date is required", "start_date_invalid": "Invalid start date", "start_date_future": "Start date must be today or in the future", "end_date_required": "End date is required", "end_date_invalid": "Invalid end date", "end_date_after_start": "End date must be after start date", "route_at_capacity": "Route is at full capacity", "client_already_subscribed_to_route": "Client already has an active subscription for this route", "price_too_high_for_route": "Price is too high for this route", "cannot_deactivate_with_pending_payments": "Cannot deactivate subscription with pending payments", "cannot_delete_route_with_subscriptions": "Cannot delete route with active subscriptions", "cannot_deactivate_route_with_active_subscriptions": "Cannot deactivate route with active subscriptions", "route_name_required": "Route name is required", "route_name_exists": "Route name already exists", "route_name_max_length": "Route name cannot exceed 255 characters", "from_area_required": "From area is required", "from_area_max_length": "From area cannot exceed 255 characters", "to_school_required": "To school is required", "to_school_max_length": "To school cannot exceed 255 characters", "pickup_time_required": "Pickup time is required", "pickup_time_format": "Pickup time must be in HH:MM format", "dropoff_time_required": "Dropoff time is required", "dropoff_time_format": "Dropoff time must be in HH:MM format", "dropoff_time_after_pickup": "Dropoff time must be after pickup time", "monthly_price_required": "Monthly price is required", "monthly_price_numeric": "Monthly price must be a number", "monthly_price_min": "Monthly price must be at least 0", "monthly_price_max": "Monthly price cannot exceed 999,999.99", "capacity_required": "Capacity is required", "capacity_integer": "Capacity must be a whole number", "capacity_min": "Capacity must be at least 1", "capacity_max": "Capacity cannot exceed 100", "capacity_below_current_students": "Capacity (:requested) cannot be less than current students (:current)", "driver_not_active": "Selected driver is not active", "driver_already_assigned": "Driver is already assigned to another route", "stops_array": "Stops must be an array", "stops_max_count": "Cannot have more than 20 stops", "stop_string": "Each stop must be text", "stop_max_length": "Each stop cannot exceed 255 characters", "cannot_delete_driver_with_active_routes": "Cannot delete driver with active route assignments", "cannot_deactivate_driver_with_active_routes": "Cannot deactivate driver with active route assignments", "driver_removed_from_route": "Driver removed from route successfully", "driver_suspended_successfully": "Driver suspended successfully", "driver_activated_successfully": "Driver activated successfully", "driver_name_required": "Driver name is required", "driver_name_max_length": "Driver name cannot exceed 255 characters", "phone_required": "Phone number is required", "phone_exists": "Phone number already exists", "phone_max_length": "Phone number cannot exceed 20 characters", "email_invalid": "Please enter a valid email address", "email_exists": "Email address already exists", "license_number_required": "License number is required", "license_number_exists": "License number already exists", "license_number_max_length": "License number cannot exceed 255 characters", "license_expiry_required": "License expiry date is required", "license_expiry_date": "License expiry must be a valid date", "license_expiry_future": "License expiry must be in the future", "license_expiry_too_far": "License expiry cannot be more than 10 years in the future", "national_id_required": "National ID is required", "national_id_exists": "National ID already exists", "national_id_max_length": "National ID cannot exceed 255 characters", "address_max_length": "Address cannot exceed 500 characters", "hire_date_required": "Hire date is required", "hire_date_date": "Hire date must be a valid date", "hire_date_past": "Hire date cannot be in the future", "hire_date_future": "Hire date cannot be in the future", "salary_numeric": "Salary must be a number", "salary_min": "Salary must be at least 0", "salary_max": "Salary cannot exceed 999,999.99", "vehicle_type_max_length": "Vehicle type cannot exceed 255 characters", "vehicle_model_max_length": "Vehicle model cannot exceed 255 characters", "vehicle_year_max_length": "Vehicle year cannot exceed 4 characters", "vehicle_plate_max_length": "Vehicle plate number cannot exceed 255 characters", "vehicle_color_max_length": "Vehicle color cannot exceed 255 characters", "vehicle_capacity_integer": "Vehicle capacity must be a whole number", "vehicle_capacity_min": "Vehicle capacity must be at least 1", "vehicle_capacity_max": "Vehicle capacity cannot exceed 100", "vehicle_notes_max_length": "Vehicle notes cannot exceed 1000 characters", "emergency_contact_name_max_length": "Emergency contact name cannot exceed 255 characters", "emergency_contact_phone_max_length": "Emergency contact phone cannot exceed 20 characters", "emergency_contact_relation_max_length": "Emergency contact relation cannot exceed 255 characters", "create_student": "Create Student", "edit_student": "Edit Student", "delete_student": "Delete Student", "student_created_successfully": "Student created successfully", "student_updated_successfully": "Student updated successfully", "student_deleted_successfully": "Student deleted successfully", "student_not_found": "Student not found", "student_name": "Student Name", "student_id": "Student ID", "grade": "Grade", "school": "School", "parent_name": "Parent Name", "parent_phone": "Parent Phone", "emergency_contact": "Emergency Contact", "financial_management": "Financial Management", "reports": "Reports", "analytics": "Analytics", "revenue": "Revenue", "expenses": "Expenses", "profit": "Profit", "loss": "Loss", "balance": "Balance", "income": "Income", "outcome": "Outcome", "financial_report": "Financial Report", "monthly_report": "Monthly Report", "yearly_report": "Yearly Report", "daily_report": "Daily Report", "system_settings": "System Settings", "general_settings": "General Settings", "email_settings": "<PERSON><PERSON>s", "notification_settings": "Notification Settings", "security_settings": "Security Settings", "backup_settings": "Backup Settings", "maintenance_mode": "Maintenance Mode", "system_information": "System Information", "version": "Version", "last_backup": "Last Backup", "storage_usage": "Storage Usage", "database_size": "Database Size", "notifications": "Notifications", "alerts": "<PERSON><PERSON><PERSON>", "messages": "Messages", "announcements": "Announcements", "mark_as_read": "<PERSON> <PERSON>", "mark_all_as_read": "<PERSON> as <PERSON>", "new_notification": "New Notification", "no_notifications": "No notifications", "audit_log": "<PERSON>t Log", "activity_log": "Activity Log", "user_activity": "User Activity", "system_activity": "System Activity", "login_activity": "Login Activity", "action_performed": "Action Performed", "user_agent": "User Agent", "timestamp": "Timestamp", "translation_management": "Translation Management", "translation_created_successfully": "Translation created successfully", "translation_updated_successfully": "Translation updated successfully", "translation_deleted_successfully": "Translation deleted successfully", "translation_file_not_found": "Translation file not found", "translations_imported_successfully": "Translations imported successfully", "translation_import_failed": "Translation import failed", "translation_key": "Translation Key", "add_translation": "Add Translation", "edit_translation": "Edit Translation", "delete_translation": "Delete Translation", "export_translations": "Export Translations", "import_translations": "Import Translations", "missing_translations": "Missing Translations", "translation_statistics": "Translation Statistics", "total_keys": "Total Keys", "translated_keys": "Translated Keys", "missing_keys": "Missing Keys", "translation_progress": "Translation Progress", "bulk_translate": "Bulk Translate", "auto_translate": "Auto Translate", "translation_source": "Translation Source", "translation_target": "Translation Target", "name_required": "Name is required", "name_min_length": "Name must be at least 2 characters", "name_max_length": "Name must not exceed 100 characters", "name_invalid_format": "Name can only contain letters and spaces", "email_required": "Email is required", "email_already_exists": "This email is already registered", "phone_invalid_format": "Please enter a valid phone number", "phone_already_exists": "This phone number is already registered", "password_required": "Password is required", "password_confirmation_mismatch": "Password confirmation does not match", "password_min_length": "Password must be at least 8 characters", "user_type_required": "User type is required", "user_type_invalid": "Selected user type is invalid", "roles_required": "At least one role is required", "roles_min_one": "Please select at least one role", "role_invalid": "Selected role is invalid", "profile_photo_must_be_image": "Profile photo must be an image", "profile_photo_invalid_format": "Profile photo must be jpeg, png, jpg, or gif", "profile_photo_too_large": "Profile photo must not exceed 2MB", "language_invalid": "Selected language is invalid", "timezone_invalid": "Selected timezone is invalid", "date_format_invalid": "Selected date format is invalid", "time_format_invalid": "Selected time format is invalid", "admin_user_requires_admin_role": "Admin users must have an admin role", "cannot_deactivate_yourself": "You cannot deactivate your own account", "user_preferences": "User Preferences", "account_settings": "Account <PERSON><PERSON>", "notification_preferences": "Notification Preferences", "cannot_delete_super_admin": "Cannot delete super admin user", "cannot_delete_yourself": "You cannot delete your own account", "user_not_deleted": "User is not deleted", "user_restored_successfully": "User restored successfully", "user_permanently_deleted": "User permanently deleted", "cannot_deactivate_super_admin": "Cannot deactivate super admin user", "user_deactivated_successfully": "User deactivated successfully", "user_activated_successfully": "User activated successfully", "restore_user": "Restore User", "permanently_delete": "Permanently Delete", "toggle_status": "Toggle Status", "user_details": "User Details", "profile_updated_successfully": "Profile updated successfully", "current_password_required": "Current password is required", "current_password_incorrect": "Current password is incorrect", "profile_photo_deleted": "Profile photo deleted", "profile_photo_deleted_successfully": "Profile photo deleted successfully", "no_profile_photo_to_delete": "No profile photo to delete", "export_user_data": "Export User Data", "delete_photo": "Delete Photo", "confirm_new_password": "Confirm New Password", "personal_information": "Personal Information", "contact_information": "Contact Information", "preferences": "Preferences", "privacy_settings": "Privacy Settings", "role_name": "Role Name", "role_description": "Role Description", "role_permissions": "Role Permissions", "role_name_required": "Role name is required", "role_name_string": "Role name must be a string", "role_name_max_length": "Role name cannot exceed 100 characters", "role_name_invalid_format": "Role name can only contain letters, numbers, spaces, hyphens and underscores", "role_name_exists": "A role with this name already exists", "role_description_string": "Role description must be a string", "role_description_max_length": "Role description cannot exceed 500 characters", "permissions_must_be_array": "Permissions must be provided as an array", "invalid_permission_selected": "One or more selected permissions are invalid", "role_created_successfully": "Role created successfully", "role_updated_successfully": "Role updated successfully", "role_deleted_successfully": "Role deleted successfully", "role_cloned_successfully": "Role cloned successfully", "role_in_use_cannot_delete": "Cannot delete role as it is assigned to users", "cannot_delete_super_admin_role": "<PERSON><PERSON> delete the super admin role", "cannot_modify_super_admin_role": "Cannot modify the super admin role", "role_name_reserved": "This role name is reserved for system use", "role_must_have_permissions": "Role must have at least one permission assigned", "role_permissions_synced": "Role permissions synchronized", "role_permissions_synced_successfully": "Role permissions synchronized successfully", "permission_description": "Permission Description", "permission_category": "Permission Category", "permission_guard": "Permission Guard", "permission_name_exists": "A permission with this name already exists", "guard_name_required": "Guard name is required", "permission_created_successfully": "Permission created successfully", "permission_updated_successfully": "Permission updated successfully", "permission_deleted_successfully": "Permission deleted successfully", "permission_in_use_cannot_delete": "Cannot delete permission as it is assigned to roles or users", "bulk_permissions_created": "Bulk permissions created", "bulk_permissions_created_count": ":count permissions created successfully", "system_management": "System Management", "system_health": "System Health", "database_status": "Database Status", "cache_status": "<PERSON>ache <PERSON>", "queue_status": "Queue Status", "memory_usage": "Memory Usage", "system_uptime": "System Uptime", "healthy": "Healthy", "connected": "Connected", "working": "Working", "normal": "Normal", "system_alerts": "System Alerts", "overdue_payments_alert": "Overdue Payments", "overdue_payments_count": "You have :count overdue payments that need attention", "expiring_subscriptions_alert": "Expiring Subscriptions", "expiring_subscriptions_count": ":count subscriptions will expire within 7 days", "drivers_on_leave_alert": "Drivers on Leave", "drivers_on_leave_count": ":count drivers are currently on leave", "failed_payments_alert": "Failed Payments", "failed_payments_count": ":count payments failed in the last 7 days", "view_payments": "View Payments", "view_subscriptions": "View Subscriptions", "review_payments": "Review Payments", "revenue_trend": "Revenue Trend", "client_growth": "Client Growth", "subscription_distribution": "Subscription Distribution", "driver_status_distribution": "Driver Status Distribution", "last_12_months": "Last 12 Months", "recent_activities": "Recent Activities", "recent_users": "Recent Users", "recent_audit_logs": "Recent Activity Logs", "no_recent_activities": "No recent activities", "this_week": "This Week", "new_this_month": "New This Month", "logged_in_today": "Logged In Today", "expiring_soon": "Expiring Soon", "suspended": "Suspended", "performance_metrics": "Performance Metrics", "usage_percentage": "Usage", "free_space": "Free Space", "total_space": "Total Space", "current_usage": "Current Usage", "memory_limit": "Memory Limit", "failed_jobs": "Failed Jobs", "uptime": "Uptime", "vs_last_week": "vs last week", "last_7_days": "Last 7 Days", "requires_attention": "Requires Attention", "weekly_revenue": "Weekly Revenue", "manage_users": "Manage Users", "new_clients": "New Clients", "create_role": "Create Role", "edit_role": "Edit Role", "view_role": "View Role", "delete_role": "Delete Role", "clone_role": "<PERSON><PERSON>", "assign_permissions": "Assign Permissions", "remove_permissions": "Remove Permissions", "deselect_all_permissions": "Deselect All Permissions", "permission_categories": "Permission Categories", "total_permissions": "Total Permissions", "total_roles": "Total Roles", "users_with_roles": "Users with Roles", "search_roles": "Search roles...", "roles_list": "Roles List", "no_description": "No description", "no_roles_found": "No roles found", "no_roles_description": "There are no roles created yet. Create your first role to get started.", "create_first_role": "Create First Role", "new_role_name": "New Role Name", "clone_role_description": "This will create a copy of the role with all its permissions.", "delete_role_warning": "This action cannot be undone!", "confirm_delete_role": "Are you sure you want to delete the role", "back_to_roles": "Back to Roles", "role_name_help": "Enter a unique name for this role (letters, numbers, spaces, hyphens, underscores only)", "role_description_help": "Optional description to explain the purpose of this role", "no_permissions_available": "No permissions available", "no_permissions_selected_confirm": "No permissions are selected. Are you sure you want to create a role without any permissions?", "select_all": "Select All", "deselect_all": "Deselect All"}